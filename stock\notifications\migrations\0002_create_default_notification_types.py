from django.db import migrations

def create_default_notification_types(apps, schema_editor):
    NotificationType = apps.get_model('notifications', 'NotificationType')
    
    notification_types = [
        {
            'nom': 'BIENVENUE',
            'description': 'Message de bienvenue pour les nouveaux utilisateurs',
            'categorie': 'USER',
            'priorite_defaut': 'LOW',
            'template_email': '''
                <h2>Bienvenue {{ nom_utilisateur }}!</h2>
                <p>Bienvenue dans notre système de gestion de stock.</p>
                <p>Votre rôle: {{ role }}</p>
                <p>Vous pouvez configurer vos préférences de notification dans votre profil.</p>
                ''',
            'template_sms': 'Bienvenue {{ nom_utilisateur }}!',
            'template_push': 'Bienvenue!',
            'template_in_app': 'Bienvenue dans le système de gestion de stock',
            'actif': True
        },
        {
            'nom': 'ALERTE_STOCK_BAS',
            'description': 'Alerte quand le stock est en dessous du seuil',
            'categorie': 'STOCK',
            'priorite_defaut': 'HIGH',
            'template_email': '''
                <h2>Alerte Stock Bas</h2>
                <p>Le produit <strong>{{ produit_nom }}</strong> ({{ produit_reference }}) est en dessous du seuil d'alerte.</p>
                <ul>
                    <li>Quantité actuelle: {{ quantite_actuelle }}</li>
                    <li>Seuil d'alerte: {{ seuil_alerte }}</li>
                    <li>Magasin: {{ magasin_nom }}</li>
                    <li>Entrepôt: {{ entrepot_nom }}</li>
                </ul>
                <p>Veuillez procéder au réapprovisionnement.</p>
                ''',
            'template_sms': 'Alerte: Stock bas pour {{ produit_nom }} ({{ quantite_actuelle }}/{{ seuil_alerte }})',
            'template_push': 'Stock bas: {{ produit_nom }}',
            'template_in_app': 'Le stock de {{ produit_nom }} est en dessous du seuil d\'alerte',
            'actif': True
        }
    ]
    
    for type_data in notification_types:
        NotificationType.objects.get_or_create(
            nom=type_data['nom'],
            defaults=type_data
        )

def remove_default_notification_types(apps, schema_editor):
    NotificationType = apps.get_model('notifications', 'NotificationType')
    NotificationType.objects.filter(nom__in=['BIENVENUE', 'ALERTE_STOCK_BAS']).delete()

class Migration(migrations.Migration):
    dependencies = [
        ('notifications', '0001_initial'),
    ]

    operations = [
        migrations.RunPython(create_default_notification_types, remove_default_notification_types),
    ] 