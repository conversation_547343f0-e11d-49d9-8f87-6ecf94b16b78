# Generated by Django 5.2 on 2025-05-20 08:11

import django.db.models.deletion
import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Entrepot',
            fields=[
                ('id_entrepot', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('nom', models.CharField(max_length=100)),
                ('adresse', models.TextField()),
                ('capacite_stockage', models.IntegerField()),
                ('statut', models.BooleanField(default=True)),
                ('type_stock', models.CharField(choices=[('FINIS', 'Produits finis'), ('MATIERE', 'Matières premières'), ('EMBALLAGE', 'Emballage et packaging'), ('SEMI_FINI', 'Produits semi-finis'), ('MARCHANDISE', 'Marchandises'), ('CONSOMMABLE', 'Consommables')], max_length=20)),
            ],
        ),
        migrations.CreateModel(
            name='Location',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('nom', models.CharField(max_length=100)),
                ('description', models.TextField(blank=True)),
                ('entrepot', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='entrepot.entrepot')),
            ],
        ),
    ]
