# Generated by Django 5.2 on 2025-05-23 11:54

import django.db.models.deletion
import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('entrepot', '0002_remove_entrepot_type_stock_location_type_stock'),
        ('entreprise', '0001_initial'),
        ('magasins', '0001_initial'),
        ('vendeurs', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='StatistiqueMagasin',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('nombre_ventes', models.PositiveIntegerField(default=0)),
                ('montant_total', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('moyenne_vente', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('magasin', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='magasins.magasin')),
            ],
        ),
        migrations.CreateModel(
            name='StatistiqueVendeur',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('nombre_ventes', models.PositiveIntegerField(default=0)),
                ('montant_total', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('moyenne_vente', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('vendeur', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='vendeurs.vendeur')),
            ],
        ),
        migrations.CreateModel(
            name='Statistique',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('total_ventes', models.PositiveIntegerField(default=0)),
                ('chiffre_affaire', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('montant_ht', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('montant_tva', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('produits_populaires', models.JSONField(default=list)),
                ('marge_brute', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('taux_marge', models.DecimalField(decimal_places=2, default=0, max_digits=5)),
                ('cout_achat', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('benefice_net', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('total_produits', models.PositiveIntegerField(default=0)),
                ('valeur_stock', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('produits_en_alerte', models.JSONField(default=list)),
                ('taux_rotation', models.DecimalField(decimal_places=2, default=0, max_digits=5)),
                ('nouveaux_clients', models.PositiveIntegerField(default=0)),
                ('clients_actifs', models.PositiveIntegerField(default=0)),
                ('panier_moyen', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('taux_fidelite', models.DecimalField(decimal_places=2, default=0, max_digits=5)),
                ('factures_emises', models.PositiveIntegerField(default=0)),
                ('factures_payees', models.PositiveIntegerField(default=0)),
                ('montant_impaye', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('delai_paiement_moyen', models.PositiveIntegerField(default=0)),
                ('temps_traitement_moyen', models.PositiveIntegerField(default=0)),
                ('taux_retour', models.DecimalField(decimal_places=2, default=0, max_digits=5)),
                ('satisfaction_client', models.DecimalField(decimal_places=2, default=0, max_digits=3)),
                ('date_statistique', models.DateField()),
                ('periode', models.CharField(choices=[('JOUR', 'Journalier'), ('SEMAINE', 'Hebdomadaire'), ('MOIS', 'Mensuel'), ('ANNEE', 'Annuel')], default='JOUR', max_length=10)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('entrepot', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='entrepot.entrepot')),
                ('entreprise', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='entreprise.entreprise')),
                ('magasin', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='magasins.magasin')),
            ],
            options={
                'ordering': ['-date_statistique', '-periode'],
                'unique_together': {('entreprise', 'magasin', 'entrepot', 'date_statistique', 'periode')},
            },
        ),
    ]
