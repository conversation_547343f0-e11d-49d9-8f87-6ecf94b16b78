# Generated by Django 5.2 on 2025-05-21 03:31

import django.db.models.deletion
import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('clients', '0001_initial'),
        ('ventes', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Facture',
            fields=[
                ('id_facture', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('numero_facture', models.CharField(max_length=50, unique=True)),
                ('nom_client', models.CharField(blank=True, max_length=100)),
                ('prenom_client', models.CharField(blank=True, max_length=100)),
                ('telephone_client', models.CharField(blank=True, max_length=15)),
                ('email_client', models.EmailField(blank=True, max_length=254, null=True)),
                ('adresse_client', models.TextField(blank=True)),
                ('nom_entreprise', models.Char<PERSON>ield(max_length=100)),
                ('adresse_entreprise', models.TextField()),
                ('telephone_entreprise', models.CharField(max_length=15)),
                ('email_entreprise', models.EmailField(blank=True, max_length=254, null=True)),
                ('nif_entreprise', models.CharField(help_text="Numéro d'identification fiscale de l'entreprise", max_length=20)),
                ('stat_entreprise', models.CharField(help_text="Numéro statistique de l'entreprise", max_length=20)),
                ('date_facture', models.DateTimeField(auto_now_add=True)),
                ('montant_ht', models.DecimalField(decimal_places=2, max_digits=10)),
                ('montant_tva', models.DecimalField(decimal_places=2, max_digits=10)),
                ('montant_total', models.DecimalField(decimal_places=2, max_digits=10)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Facture',
                'verbose_name_plural': 'Factures',
            },
        ),
        migrations.AlterModelOptions(
            name='client',
            options={'verbose_name': 'Client', 'verbose_name_plural': 'Clients'},
        ),
        migrations.RenameField(
            model_name='client',
            old_name='id',
            new_name='id_client',
        ),
        migrations.AlterField(
            model_name='client',
            name='adresse',
            field=models.TextField(default=1),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name='client',
            name='telephone',
            field=models.CharField(max_length=15),
        ),
        migrations.AddIndex(
            model_name='client',
            index=models.Index(fields=['nom', 'prenom'], name='clients_cli_nom_f99d14_idx'),
        ),
        migrations.AddIndex(
            model_name='client',
            index=models.Index(fields=['telephone'], name='clients_cli_telepho_8c69d9_idx'),
        ),
        migrations.AddIndex(
            model_name='client',
            index=models.Index(fields=['email'], name='clients_cli_email_56b9fc_idx'),
        ),
        migrations.AddField(
            model_name='facture',
            name='client',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='clients.client'),
        ),
        migrations.AddField(
            model_name='facture',
            name='vente',
            field=models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='facture', to='ventes.vente'),
        ),
        migrations.AddIndex(
            model_name='facture',
            index=models.Index(fields=['numero_facture'], name='clients_fac_numero__422389_idx'),
        ),
        migrations.AddIndex(
            model_name='facture',
            index=models.Index(fields=['date_facture'], name='clients_fac_date_fa_b545c3_idx'),
        ),
        migrations.AddIndex(
            model_name='facture',
            index=models.Index(fields=['client'], name='clients_fac_client__09f12c_idx'),
        ),
    ]
