#!/usr/bin/env python
"""
Script de test pour le nouveau système d'authentification
"""
import os
import sys
import django

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'stock.settings')
django.setup()

from django.test import TestCase
from django.contrib.auth import get_user_model
from rest_framework.test import APIClient
from rest_framework import status
from base.models import User
from magasins.models import Magasin
from entreprise.models import Entreprise
from entrepot.models import Entrepot

User = get_user_model()

def test_registration_creates_admin_only():
    """Test que l'inscription crée seulement des admins"""
    print("🧪 Test: Registration crée seulement des admins")

    client = APIClient()

    data = {
        "nom": "Admin",
        "prenom": "Test",
        "telephone": "1234567890",
        "password": "testpass123",
        "adresse": "123 Admin Street"
    }

    response = client.post('/register/', data)
    print(f"Status: {response.status_code}")

    if response.status_code == 201:
        user = User.objects.get(telephone="1234567890")
        print(f"✅ Utilisateur créé avec le rôle: {user.role}")
        assert user.role == 'ADMIN', f"Expected ADMIN, got {user.role}"
        print("✅ Test réussi: Registration crée bien un admin")
    else:
        print(f"❌ Erreur: {response.data}")

def test_responsable_magasin_creation():
    """Test de création d'un responsable magasin"""
    print("\n🧪 Test: Création responsable magasin")

    # Créer un admin pour les tests
    admin = User.objects.create_user(
        username='admin_test',
        telephone='0000000000',
        nom='Admin',
        prenom='Test',
        password='testpass123',
        role='ADMIN'
    )

    # Créer une entreprise et un magasin pour les tests
    try:
        entreprise = Entreprise.objects.create(
            nom="Test Entreprise",
            adresse="123 Test Street",
            nif="1234567890123"
        )

        magasin = Magasin.objects.create(
            nom="Test Magasin",
            adresse="456 Magasin Street",
            entreprise=entreprise
        )

        client = APIClient()
        client.force_authenticate(user=admin)

        data = {
            "nom": "Responsable",
            "prenom": "Test",
            "telephone": "1111111111",
            "password": "testpass123",
            "magasin": magasin.id,
            "date_embauche": "2024-01-15",
            "adresse": "789 Resp Street"
        }

        response = client.post('/api/responsables-magasin/', data)
        print(f"Status: {response.status_code}")

        if response.status_code == 201:
            print("✅ Responsable magasin créé avec succès")
            print(f"Response: {response.data}")
        else:
            print(f"❌ Erreur: {response.data}")

    except Exception as e:
        print(f"❌ Erreur lors de la création des objets de test: {e}")

def test_vendeur_creation():
    """Test de création d'un vendeur"""
    print("\n🧪 Test: Création vendeur")

    # Utiliser l'admin existant
    try:
        admin = User.objects.get(username='admin_test')
        magasin = Magasin.objects.first()

        if not magasin:
            print("❌ Aucun magasin trouvé pour le test")
            return

        client = APIClient()
        client.force_authenticate(user=admin)

        data = {
            "nom": "Vendeur",
            "prenom": "Test",
            "telephone": "2222222222",
            "password": "testpass123",
            "magasin": magasin.id,
            "date_embauche": "2024-01-20",
            "taux_commission": 0.15,
            "adresse": "321 Vendeur Street"
        }

        response = client.post('/api/vendeurs/', data)
        print(f"Status: {response.status_code}")

        if response.status_code == 201:
            print("✅ Vendeur créé avec succès")
            print(f"Response: {response.data}")
        else:
            print(f"❌ Erreur: {response.data}")

    except User.DoesNotExist:
        print("❌ Admin de test non trouvé")
    except Exception as e:
        print(f"❌ Erreur: {e}")

def test_responsable_entrepot_creation():
    """Test de création d'un responsable entrepôt"""
    print("\n🧪 Test: Création responsable entrepôt")

    # Utiliser l'admin existant
    try:
        admin = User.objects.get(username='admin_test')

        # Créer un entrepôt pour le test
        entrepot = Entrepot.objects.create(
            nom="Test Entrepôt",
            adresse="789 Entrepôt Street",
            capacite_stockage=5000
        )

        client = APIClient()
        client.force_authenticate(user=admin)

        data = {
            "nom": "Responsable",
            "prenom": "Entrepôt",
            "telephone": "3333333333",
            "password": "testpass123",
            "entrepot": entrepot.id,
            "date_embauche": "2024-01-25",
            "adresse": "456 Resp Entrepôt Street"
        }

        response = client.post('/api/responsables-entrepot/', data)
        print(f"Status: {response.status_code}")

        if response.status_code == 201:
            print("✅ Responsable entrepôt créé avec succès")
            print(f"Response: {response.data}")
        else:
            print(f"❌ Erreur: {response.data}")

    except User.DoesNotExist:
        print("❌ Admin de test non trouvé")
    except Exception as e:
        print(f"❌ Erreur: {e}")

def cleanup_test_data():
    """Nettoyer les données de test"""
    print("\n🧹 Nettoyage des données de test...")

    # Supprimer les utilisateurs de test
    User.objects.filter(telephone__in=['1234567890', '0000000000', '1111111111', '2222222222', '3333333333']).delete()

    # Supprimer les objets de test
    Magasin.objects.filter(nom="Test Magasin").delete()
    Entreprise.objects.filter(nom="Test Entreprise").delete()
    Entrepot.objects.filter(nom="Test Entrepôt").delete()

    print("✅ Nettoyage terminé")

if __name__ == "__main__":
    print("🚀 Démarrage des tests du nouveau système d'authentification\n")

    try:
        test_registration_creates_admin_only()
        test_responsable_magasin_creation()
        test_vendeur_creation()
        test_responsable_entrepot_creation()
    except Exception as e:
        print(f"❌ Erreur générale: {e}")
    finally:
        cleanup_test_data()

    print("\n✅ Tests terminés")
