# Generated by Django 5.2 on 2025-05-20 08:11

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Fournisseur',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('reference_fiscale', models.CharField(blank=True, max_length=50, null=True, unique=True)),
                ('categorie_produits', models.CharField(max_length=100)),
                ('statut', models.CharField(choices=[('ACTIF', 'Actif'), ('INACTIF', 'Inactif')], default='ACTIF', max_length=10)),
                ('notes', models.TextField(blank=True)),
                ('user', models.OneToOneField(limit_choices_to={'role': 'FOURNISSEUR'}, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
        ),
    ]
