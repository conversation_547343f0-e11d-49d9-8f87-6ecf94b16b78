from django.db import migrations, models
import django.db.models.deletion

def create_default_entrepot(apps, schema_editor):
    Entrepot = apps.get_model('entrepot', 'Entrepot')
    Magasin = apps.get_model('magasins', 'Magasin')
    
    # Créer un entrepôt par défaut si nécessaire
    default_entrepot, created = Entrepot.objects.get_or_create(
        id_entrepot='00000000-0000-0000-0000-000000000001',
        defaults={
            'nom': 'Entrepôt Principal',
            'adresse': 'Adresse par défaut',
            'capacite_stockage': 1000,
            'statut': True
        }
    )
    
    # Mettre à jour tous les magasins existants
    Magasin.objects.all().update(entrepot=default_entrepot)

class Migration(migrations.Migration):

    dependencies = [
        ('entrepot', '0001_initial'),
        ('magasins', '0004_remove_entrepot'),
    ]

    operations = [
        migrations.AddField(
            model_name='magasin',
            name='entrepot',
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                to='entrepot.entrepot',
                verbose_name='Entrepôt associé'
            ),
        ),
        migrations.RunPython(create_default_entrepot),
        migrations.AlterField(
            model_name='magasin',
            name='entrepot',
            field=models.ForeignKey(
                null=False,
                on_delete=django.db.models.deletion.CASCADE,
                to='entrepot.entrepot',
                verbose_name='Entrepôt associé'
            ),
        ),
    ] 