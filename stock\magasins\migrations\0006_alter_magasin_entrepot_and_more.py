# Generated by Django 5.2 on 2025-06-17 09:16

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('entrepot', '0002_remove_entrepot_type_stock_location_type_stock'),
        ('entreprise', '0001_initial'),
        ('magasins', '0005_add_entrepot'),
        ('responsable_magasin', '0002_alter_responsable_magasin_options_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='magasin',
            name='entrepot',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='magasins', to='entrepot.entrepot'),
        ),
        migrations.AddIndex(
            model_name='magasin',
            index=models.Index(fields=['entrepot'], name='magasins_ma_entrepo_52c162_idx'),
        ),
    ]
