# Generated by Django 5.2 on 2025-05-24 09:31

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models
from django.utils import timezone


class Migration(migrations.Migration):

    dependencies = [
        ('entrepot', '0002_remove_entrepot_type_stock_location_type_stock'),
        ('inventaire', '0001_initial'),
        ('magasins', '0001_initial'),
        ('produit', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='inventaire',
            options={'ordering': ['-date_planification'], 'verbose_name': 'Inventaire', 'verbose_name_plural': 'Inventaires'},
        ),
        migrations.RemoveIndex(
            model_name='inventaire',
            name='inventaire__date_in_723743_idx',
        ),
        migrations.RemoveField(
            model_name='inventaire',
            name='date_inventaire',
        ),
        migrations.AddField(
            model_name='detailinventaire',
            name='commentaire',
            field=models.TextField(blank=True),
        ),
        migrations.AddField(
            model_name='detailinventaire',
            name='compteur',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='comptages', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='detailinventaire',
            name='date_comptage',
            field=models.DateTimeField(auto_now_add=True),
        ),
        migrations.AddField(
            model_name='inventaire',
            name='commentaire',
            field=models.TextField(blank=True),
        ),
        migrations.AddField(
            model_name='inventaire',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True),
        ),
        migrations.AddField(
            model_name='inventaire',
            name='date_debut',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='inventaire',
            name='date_fin',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='inventaire',
            name='date_planification',
            field=models.DateField(default=timezone.now),
        ),
        migrations.AddField(
            model_name='inventaire',
            name='numero_inventaire',
            field=models.CharField(default=1, max_length=50, unique=True),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='inventaire',
            name='tolerance_ecart',
            field=models.DecimalField(decimal_places=2, default=0.0, help_text="Tolérance d'écart en pourcentage", max_digits=5),
        ),
        migrations.AddField(
            model_name='inventaire',
            name='type_inventaire',
            field=models.CharField(choices=[('COMPLET', 'Inventaire complet'), ('PARTIEL', 'Inventaire partiel'), ('CYCLIQUE', 'Inventaire cyclique'), ('ALERTE', 'Inventaire suite à alerte')], default='COMPLET', max_length=20),
        ),
        migrations.AddField(
            model_name='inventaire',
            name='updated_at',
            field=models.DateTimeField(auto_now=True),
        ),
        migrations.AlterField(
            model_name='inventaire',
            name='statut',
            field=models.CharField(choices=[('PLANIFIE', 'Planifié'), ('EN_COURS', 'En cours'), ('TERMINE', 'Terminé'), ('ANNULE', 'Annulé')], default='PLANIFIE', max_length=20),
        ),
        migrations.AlterUniqueTogether(
            name='detailinventaire',
            unique_together={('inventaire', 'produit')},
        ),
        migrations.AddIndex(
            model_name='detailinventaire',
            index=models.Index(fields=['date_comptage'], name='inventaire__date_co_7e1a9d_idx'),
        ),
        migrations.AddIndex(
            model_name='inventaire',
            index=models.Index(fields=['date_planification'], name='inventaire__date_pl_474d64_idx'),
        ),
        migrations.AddIndex(
            model_name='inventaire',
            index=models.Index(fields=['statut'], name='inventaire__statut_a771d3_idx'),
        ),
        migrations.AddIndex(
            model_name='inventaire',
            index=models.Index(fields=['type_inventaire'], name='inventaire__type_in_fd21e5_idx'),
        ),
    ]
