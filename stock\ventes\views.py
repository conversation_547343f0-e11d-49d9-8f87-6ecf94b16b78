from rest_framework import status
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.permissions import IsAuthenticated
from .models import Vente, DetailVente
from .serializers import VenteSerializer, DetailVenteSerializer
from base.views import IsAdmin, isResponsable_Magasin
from django.db.models import Q
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi

class VenteListCreateView(APIView):
    # permission_classes = [IsAuthenticated]

    @swagger_auto_schema(
        operation_description="Récupérer la liste des ventes",
        manual_parameters=[
            openapi.Parameter(
                'magasin_id',
                openapi.IN_QUERY,
                description="ID du magasin pour filtrer les ventes",
                type=openapi.TYPE_STRING,
                format=openapi.FORMAT_UUID,
                required=False
            )
        ],
        responses={
            200: openapi.Response(
                description="Liste des ventes",
                schema=openapi.Schema(
                    type=openapi.TYPE_ARRAY,
                    items=openapi.Schema(type=openapi.TYPE_OBJECT)
                )
            ),
            400: "Bad Request"
        }
    )
    def get(self, request):
        # Récupérer le magasin_id des paramètres de requête
        magasin_id = request.query_params.get('magasin_id')
        
        # Filtrer les ventes par magasin si spécifié
        if magasin_id:
            ventes = Vente.objects.filter(magasin_id=magasin_id)
        else:
            # Si l'utilisateur est un vendeur, ne montrer que ses ventes
            if request.user.role == 'VENDEUR':
                ventes = Vente.objects.filter(vendeur__user=request.user)
            # Si l'utilisateur est un responsable de magasin, montrer les ventes de son magasin
            elif request.user.role == 'RESPONSABLE_MAGASIN':
                ventes = Vente.objects.filter(magasin__responsable_magasin__user=request.user)
            else:
                ventes = Vente.objects.all()

        serializer = VenteSerializer(ventes, many=True)
        return Response(serializer.data)

    @swagger_auto_schema(
        operation_description="Créer une nouvelle vente",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=['magasin', 'mode_paiement', 'produits'],
            properties={
                'magasin': openapi.Schema(type=openapi.TYPE_STRING, format=openapi.FORMAT_UUID),
                'client': openapi.Schema(type=openapi.TYPE_STRING, format=openapi.FORMAT_UUID),
                'vendeur': openapi.Schema(type=openapi.TYPE_STRING, format=openapi.FORMAT_UUID),
                'mode_paiement': openapi.Schema(type=openapi.TYPE_STRING),
                'produits': openapi.Schema(
                    type=openapi.TYPE_ARRAY,
                    items=openapi.Schema(
                        type=openapi.TYPE_OBJECT,
                        properties={
                            'produit': openapi.Schema(type=openapi.TYPE_STRING, format=openapi.FORMAT_UUID),
                            'quantite': openapi.Schema(type=openapi.TYPE_INTEGER, minimum=1)
                        }
                    )
                )
            }
        ),
        responses={
            201: openapi.Response(
                description="Vente créée avec succès",
                schema=openapi.Schema(type=openapi.TYPE_OBJECT)
            ),
            400: "Bad Request"
        }
    )
    def post(self, request):
        # Vérifier les données requises
        magasin_id = request.data.get('magasin')
        produits = request.data.get('produits', [])
        mode_paiement = request.data.get('mode_paiement')

        if not magasin_id:
            return Response(
                {"error": "Le magasin est requis"},
                status=status.HTTP_400_BAD_REQUEST
            )

        if not produits:
            return Response(
                {"error": "Au moins un produit est requis"},
                status=status.HTTP_400_BAD_REQUEST
            )

        if not mode_paiement:
            return Response(
                {"error": "Le mode de paiement est requis"},
                status=status.HTTP_400_BAD_REQUEST
            )

        if mode_paiement not in dict(Vente.MODE_PAIEMENT_CHOICES):
            return Response(
                {"error": "Mode de paiement invalide"},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Vérifier que le vendeur appartient au magasin
        vendeur_id = request.data.get('vendeur')
        if vendeur_id:
            from vendeurs.models import Vendeur
            try:
                vendeur = Vendeur.objects.get(id=vendeur_id)
                if vendeur.magasin_id != magasin_id:
                    return Response(
                        {"error": "Le vendeur doit appartenir au magasin spécifié"},
                        status=status.HTTP_400_BAD_REQUEST
                    )
            except Vendeur.DoesNotExist:
                return Response(
                    {"error": "Vendeur non trouvé"},
                    status=status.HTTP_404_NOT_FOUND
                )

        try:
            # Créer la vente
            vente_data = {
                'magasin': magasin_id,
                'client': request.data.get('client'),
                'vendeur': vendeur_id,
                'mode_paiement': mode_paiement
            }
            serializer = VenteSerializer(data=vente_data)
            if serializer.is_valid():
                vente = serializer.save()

                # Ajouter les produits
                for produit_data in produits:
                    produit_id = produit_data.get('produit')
                    quantite = produit_data.get('quantite')

                    if not produit_id or not quantite:
                        vente.delete()  # Annuler la vente si un produit est invalide
                        return Response(
                            {"error": "Produit et quantité requis pour chaque produit"},
                            status=status.HTTP_400_BAD_REQUEST
                        )

                    try:
                        detail = DetailVente.ajouterProduit(vente.id_vente, produit_id, quantite)
                    except Exception as e:
                        vente.delete()  # Annuler la vente en cas d'erreur
                        return Response(
                            {"error": str(e)},
                            status=status.HTTP_400_BAD_REQUEST
                        )

                # Recharger la vente pour avoir les montants à jour
                vente.refresh_from_db()
                return Response(VenteSerializer(vente).data, status=status.HTTP_201_CREATED)
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            return Response(
                {"error": str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )

class VenteDetailView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request, id):
        try:
            vente = Vente.objects.get(id_vente=id)
            # Vérifier les permissions
            if request.user.role == 'VENDEUR' and vente.vendeur.user != request.user:
                return Response(
                    {"error": "Vous n'avez pas accès à cette vente"},
                    status=status.HTTP_403_FORBIDDEN
                )
            if request.user.role == 'RESPONSABLE_MAGASIN' and vente.magasin.responsable_magasin.user != request.user:
                return Response(
                    {"error": "Vous n'avez pas accès à cette vente"},
                    status=status.HTTP_403_FORBIDDEN
                )
            
            serializer = VenteSerializer(vente)
            return Response(serializer.data)
        except Vente.DoesNotExist:
            return Response(
                {"error": "Vente non trouvée"},
                status=status.HTTP_404_NOT_FOUND
            )

    def put(self, request, id):
        try:
            vente = Vente.objects.get(id=id)
            serializer = VenteSerializer(vente, data=request.data, partial=True)
            if serializer.is_valid():
                serializer.save()
                return Response(serializer.data)
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        except Vente.DoesNotExist:
            return Response({"error": "Vente non trouvée"}, status=status.HTTP_404_NOT_FOUND)

    def delete(self, request, id):
        try:
            vente = Vente.objects.get(id=id)
            vente.delete()
            return Response(status=status.HTTP_204_NO_CONTENT)
        except Vente.DoesNotExist:
            return Response({"error": "Vente non trouvée"}, status=status.HTTP_404_NOT_FOUND)

class AddProduitVenteView(APIView):
    permission_classes = [IsAuthenticated]

    @swagger_auto_schema(
        operation_description="Ajouter un produit à une vente existante",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=['produit', 'quantite'],
            properties={
                'produit': openapi.Schema(type=openapi.TYPE_STRING, format=openapi.FORMAT_UUID),
                'quantite': openapi.Schema(type=openapi.TYPE_INTEGER, minimum=1)
            }
        ),
        responses={
            201: openapi.Response(
                description="Produit ajouté à la vente",
                schema=openapi.Schema(type=openapi.TYPE_OBJECT)
            ),
            400: "Bad Request",
            403: "Forbidden",
            404: "Not Found"
        }
    )
    def post(self, request, vente_id):
        try:
            vente = Vente.objects.get(id_vente=vente_id)
            # Vérifier les permissions
            if request.user.role == 'VENDEUR' and vente.vendeur.user != request.user:
                return Response(
                    {"error": "Vous n'avez pas accès à cette vente"},
                    status=status.HTTP_403_FORBIDDEN
                )
            
            produit_id = request.data.get('produit')
            quantite = request.data.get('quantite')
            
            if not produit_id or not quantite:
                return Response(
                    {"error": "Produit et quantité requis"},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # Vérifier que le produit appartient au magasin
            from produit.models import Produit
            try:
                produit = Produit.objects.get(id_produit=produit_id)
                if produit.magasin != vente.magasin:
                    return Response(
                        {"error": "Le produit doit appartenir au magasin de la vente"},
                        status=status.HTTP_400_BAD_REQUEST
                    )
            except Produit.DoesNotExist:
                return Response(
                    {"error": "Produit non trouvé"},
                    status=status.HTTP_404_NOT_FOUND
                )
            
            detail = DetailVente.ajouterProduit(vente_id, produit_id, quantite)
            serializer = DetailVenteSerializer(detail)
            return Response(serializer.data, status=status.HTTP_201_CREATED)
            
        except Vente.DoesNotExist:
            return Response(
                {"error": "Vente non trouvée"},
                status=status.HTTP_404_NOT_FOUND
            )

class FinaliserVenteView(APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request, id):
        try:
            vente = Vente.objects.get(id_vente=id)
            
            # Vérifier qu'il y a des produits dans la vente
            if not vente.details.exists():
                return Response(
                    {"error": "Impossible de finaliser une vente sans produits"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Vérifier que le montant total est supérieur à 0
            if vente.montant_total <= 0:
                return Response(
                    {"error": "Le montant total doit être supérieur à 0"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Mettre à jour le mode de paiement
            mode_paiement = request.data.get('mode_paiement')
            if not mode_paiement:
                return Response(
                    {"error": "Le mode de paiement est requis"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            if mode_paiement not in dict(Vente.MODE_PAIEMENT_CHOICES):
                return Response(
                    {"error": "Mode de paiement invalide"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            vente.mode_paiement = mode_paiement
            vente.save()

            return Response(VenteSerializer(vente).data)

        except Vente.DoesNotExist:
            return Response(
                {"error": "Vente non trouvée"},
                status=status.HTTP_404_NOT_FOUND
            )