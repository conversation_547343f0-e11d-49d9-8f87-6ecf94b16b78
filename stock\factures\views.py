from rest_framework import status
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.permissions import IsAuthenticated
from .models import Facture
from .serializers import FactureSerializer
from stock.ventes.models import Vente

class FactureView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request, vente_id):
        try:
            vente = Vente.objects.get(id_vente=vente_id)
            # Vérifier si une facture existe déjà pour cette vente
            try:
                facture = Facture.objects.get(vente=vente)
                serializer = FactureSerializer(facture)
                return Response(serializer.data)
            except Facture.DoesNotExist:
                # Générer une nouvelle facture
                facture = Facture.generer_facture(vente)
                serializer = FactureSerializer(facture)
                return Response(serializer.data, status=status.HTTP_201_CREATED)
        except Vente.DoesNotExist:
            return Response(
                {"error": "Vente non trouvée"},
                status=status.HTTP_404_NOT_FOUND
            ) 