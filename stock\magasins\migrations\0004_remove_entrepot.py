from django.db import migrations

class Migration(migrations.Migration):

    dependencies = [
        ('magasins', '0003_auto_20250617_1201'),
    ]

    operations = [
        migrations.RunSQL(
            # Supprimer la contrainte de clé étrangère si elle existe
            sql="""
            DO $$ 
            BEGIN
                IF EXISTS (
                    SELECT 1 
                    FROM information_schema.table_constraints 
                    WHERE constraint_name = 'magasins_magasin_entrepot_id_a537c07b_fk_entrepot_'
                ) THEN
                    ALTER TABLE magasins_magasin 
                    DROP CONSTRAINT magasins_magasin_entrepot_id_a537c07b_fk_entrepot_;
                END IF;
            END $$;
            """,
            reverse_sql=migrations.RunSQL.noop
        ),
        migrations.RunSQL(
            # Supprimer la colonne si elle existe
            sql="ALTER TABLE magasins_magasin DROP COLUMN IF EXISTS entrepot_id;",
            reverse_sql=migrations.RunSQL.noop
        ),
    ] 