# Generated by Django 5.2 on 2025-06-17 07:33

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('vendeurs', '0002_remove_vendeur_code_vendeur'),
    ]

    operations = [
        migrations.AddField(
            model_name='vendeur',
            name='code_vendeur',
            field=models.CharField(default=1, max_length=20, unique=True, validators=[django.core.validators.RegexValidator('^VEND-[0-9]{6}$', 'Le code doit être au format VEND-XXXXXX')]),
            preserve_default=False,
        ),
    ]
