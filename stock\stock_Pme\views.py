from rest_framework import status
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.permissions import IsAuthenticated
from django.views.decorators.cache import cache_page
from django.utils.decorators import method_decorator
from django.db import models
import uuid

from .models import Stock
from .serializers import StockSerializer
from .services import StockService
from entrepot.models import Entrepot

# Performance imports
try:
    from performance.cache_service import CacheService, StockCacheManager, cache_result
    from performance.query_optimizer import StockQueryOptimizer
    from performance.monitoring import monitor_performance
    PERFORMANCE_ENABLED = True
except ImportError:
    # Fallback si le module performance n'est pas disponible
    PERFORMANCE_ENABLED = False
    def cache_result(*args, **kwargs):
        def decorator(func):
            return func
        return decorator
    def monitor_performance(*args, **kwargs):
        def decorator(func):
            return func
        return decorator

class CapaciteEntrepotAPIView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request, entrepot_id):
        try:
            entrepot = Entrepot.objects.get(id_entrepot=entrepot_id)
            result = StockService.verifier_capacite_entrepot(entrepot)
            return Response(result)
        except Entrepot.DoesNotExist:
            return Response(
                {"error": "Entrepôt non trouvé"},
                status=status.HTTP_404_NOT_FOUND
            )

class RapportStockAPIView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request, entrepot_id):
        try:
            entrepot = Entrepot.objects.get(id_entrepot=entrepot_id)
            rapport = StockService.generer_rapport_stock(entrepot)
            return Response(rapport)
        except Entrepot.DoesNotExist:
            return Response(
                {"error": "Entrepôt non trouvé"},
                status=status.HTTP_404_NOT_FOUND
            )

class MouvementStockAPIView(APIView):
    

    # POST pour ajouter un mouvement de stock
    def post(self, request):
        try:
            produit_id = uuid.UUID(request.data.get('produit_id'))
            quantite = int(request.data.get('quantite'))
            type_mouvement = request.data.get('type_mouvement')
            entrepot_id = request.data.get('entrepot_id')  # Optionnel
            magasin_id = request.data.get('magasin_id')    # Optionnel

            if type_mouvement not in ['ENTREE', 'SORTIE']:
                raise ValueError("Type de mouvement invalide")

            result = StockService.executer_mouvement(
                produit_id=produit_id,
                quantite=quantite,
                type_mouvement=type_mouvement,
                utilisateur=request.user.username,
                notes=request.data.get('notes'),
                entrepot_id=entrepot_id,
                magasin_id=magasin_id
            )

            return Response({
                'success': True,
                'quantite_actuelle': result['nouvelle_quantite'],
                'mouvement_id': result['mouvement'].id_mouvement,
                'location': result['location'],
                'entrepot': result['entrepot'],
                'magasin': result['magasin']
            })

        except Exception as e:
            return Response(
                {"error": str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )

    # GET pour récupérer les mouvements de stock
    def get(self, request):
        try:
            # Filtrer les mouvements selon les paramètres
            mouvement_type = request.GET.get('type_mouvement')
            start_date = request.GET.get('start_date')
            end_date = request.GET.get('end_date')

            # Récupérer tous les mouvements de stock
            mouvements = Stock.objects.all()

            # Appliquer le filtrage du type de mouvement, si fourni
            if mouvement_type:
                mouvements = mouvements.filter(type_mouvement=mouvement_type)

            # Appliquer le filtrage par date, si fourni
            if start_date:
                mouvements = mouvements.filter(date_mouvement__gte=start_date)
            if end_date:
                mouvements = mouvements.filter(date_mouvement__lte=end_date)

            # Sérialisation des résultats
            mouvements_data = [
                {
                    'id_mouvement': mouvement.id_mouvement,
                    'date_mouvement': mouvement.date_mouvement,
                    'produit': mouvement.produit.nom,
                    'type_mouvement': mouvement.type_mouvement,
                    'quantite': mouvement.quantite,
                    'notes': mouvement.notes,
                    'utilisateur': mouvement.utilisateur,
                    'location': mouvement.location.nom,
                    'entrepot': mouvement.location.entrepot.nom,
                }
                for mouvement in mouvements
            ]

            return Response(mouvements_data)

        except Exception as e:
            return Response(
                {"error": str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )

class AlerteStockAPIView(APIView):
    permission_classes = [IsAuthenticated]

    @monitor_performance('stock_alerts_api')
    def get(self, request):
        """Récupère les alertes de stock avec cache optimisé"""

        # Paramètres de filtrage
        magasin_id = request.GET.get('magasin_id')

        # Essayer de récupérer du cache d'abord
        if PERFORMANCE_ENABLED:
            cached_alerts = StockCacheManager.get_cached_stock_alerts(magasin_id or 'all')
            if cached_alerts is not None:
                return Response(cached_alerts)

        # Si pas en cache, récupérer de la DB avec requête optimisée
        if PERFORMANCE_ENABLED:
            stocks = StockQueryOptimizer.get_stock_alerts(magasin_id)
        else:
            # Fallback sans optimisation
            stocks = Stock.objects.filter(
                quantite_disponible__lt=models.F('seuil_alerte')
            ).select_related('produit', 'location__entrepot')

            if magasin_id:
                stocks = stocks.filter(magasin_id=magasin_id)

        # Construire le résultat
        result = []
        for stock in stocks:
            stock_data = {
                'id_stock': str(stock.id_stock),
                'produit': stock.produit.nom,
                'reference': stock.produit.reference,
                'quantite': stock.quantite_disponible,
                'seuil': stock.seuil_alerte,
                'emplacement': stock.location.nom if stock.location else 'N/A',
                'entrepot': stock.location.entrepot.nom if stock.location and stock.location.entrepot else 'N/A',
                'magasin': stock.magasin.nom if stock.magasin else 'N/A',
                'criticite': 'CRITIQUE' if stock.quantite_disponible == 0 else 'ALERTE',
                'pourcentage_restant': (stock.quantite_disponible / stock.seuil_alerte * 100) if stock.seuil_alerte > 0 else 0
            }
            result.append(stock_data)

        # Mettre en cache le résultat
        if PERFORMANCE_ENABLED:
            StockCacheManager.cache_stock_alerts(magasin_id or 'all', result)

        return Response({
            'count': len(result),
            'alerts': result,
            'summary': {
                'total_alerts': len(result),
                'critical_alerts': len([r for r in result if r['criticite'] == 'CRITIQUE']),
                'warning_alerts': len([r for r in result if r['criticite'] == 'ALERTE'])
            }
        })


class StockSummaryAPIView(APIView):
    """Vue optimisée pour le résumé de stock avec cache"""
    permission_classes = [IsAuthenticated]

    @monitor_performance('stock_summary_api')
    @cache_result(timeout=300, key_prefix='stock_summary')  # Cache 5 minutes
    def get(self, request):
        """Récupère un résumé complet du stock"""

        magasin_id = request.GET.get('magasin_id')

        # Essayer de récupérer du cache
        cache_key = f"stock_summary_{magasin_id or 'all'}"
        if PERFORMANCE_ENABLED:
            cached_summary = CacheService.get(cache_key)
            if cached_summary:
                return Response(cached_summary)

        # Récupérer les données avec requête optimisée
        if PERFORMANCE_ENABLED:
            summary = StockQueryOptimizer.get_stock_summary(magasin_id)
        else:
            # Fallback sans optimisation
            from django.db.models import Sum, Count, F, Case, When, IntegerField

            queryset = Stock.objects.all()
            if magasin_id:
                queryset = queryset.filter(magasin_id=magasin_id)

            summary = queryset.aggregate(
                total_produits=Count('id_stock'),
                total_quantite=Sum('quantite_disponible'),
                produits_en_alerte=Count(
                    Case(
                        When(quantite_disponible__lte=F('seuil_alerte'), then=1),
                        output_field=IntegerField()
                    )
                ),
                produits_epuises=Count(
                    Case(
                        When(quantite_disponible=0, then=1),
                        output_field=IntegerField()
                    )
                )
            )

        # Enrichir le résumé
        result = {
            'timestamp': models.functions.Now(),
            'magasin_id': magasin_id,
            'summary': summary,
            'status': {
                'healthy': summary.get('produits_en_alerte', 0) == 0,
                'needs_attention': summary.get('produits_en_alerte', 0) > 0,
                'critical': summary.get('produits_epuises', 0) > 0
            },
            'percentages': {
                'products_in_alert': (summary.get('produits_en_alerte', 0) / summary.get('total_produits', 1)) * 100,
                'products_out_of_stock': (summary.get('produits_epuises', 0) / summary.get('total_produits', 1)) * 100
            }
        }

        # Mettre en cache
        if PERFORMANCE_ENABLED:
            CacheService.set(cache_key, result, 300)  # 5 minutes

        return Response(result)


class StockPerformanceAPIView(APIView):
    """Vue pour les métriques de performance du système de stock"""
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """Récupère les métriques de performance"""

        if not PERFORMANCE_ENABLED:
            return Response({
                'error': 'Module de performance non disponible'
            }, status=status.HTTP_503_SERVICE_UNAVAILABLE)

        try:
            from performance.monitoring import PerformanceMonitor, CacheMonitor

            # Métriques système
            system_metrics = PerformanceMonitor.get_system_metrics()

            # Métriques cache
            cache_stats = CacheMonitor.get_cache_stats()
            cache_hit_ratio = CacheMonitor.get_hit_ratio()

            # Métriques application
            app_metrics = PerformanceMonitor.get_application_metrics()

            return Response({
                'timestamp': models.functions.Now(),
                'system': {
                    'cpu_percent': system_metrics.get('cpu', {}).get('percent', 0),
                    'memory_percent': system_metrics.get('memory', {}).get('percent', 0),
                    'disk_percent': system_metrics.get('disk', {}).get('percent', 0)
                },
                'cache': {
                    'hit_ratio': cache_hit_ratio,
                    'stats': cache_stats
                },
                'application': {
                    'stock_alerts': app_metrics.get('stock', {}).get('alerts', 0),
                    'active_users': app_metrics.get('users', {}).get('active_24h', 0)
                },
                'recommendations': self._get_performance_recommendations(
                    system_metrics, cache_hit_ratio, app_metrics
                )
            })

        except Exception as e:
            return Response({
                'error': f'Erreur récupération métriques: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def _get_performance_recommendations(self, system_metrics, cache_hit_ratio, app_metrics):
        """Génère des recommandations de performance"""
        recommendations = []

        # Recommandations CPU
        cpu_percent = system_metrics.get('cpu', {}).get('percent', 0)
        if cpu_percent > 80:
            recommendations.append({
                'type': 'cpu',
                'level': 'warning',
                'message': 'Utilisation CPU élevée, considérer l\'optimisation des requêtes'
            })

        # Recommandations mémoire
        memory_percent = system_metrics.get('memory', {}).get('percent', 0)
        if memory_percent > 85:
            recommendations.append({
                'type': 'memory',
                'level': 'warning',
                'message': 'Utilisation mémoire élevée, considérer l\'augmentation du cache'
            })

        # Recommandations cache
        if cache_hit_ratio < 70:
            recommendations.append({
                'type': 'cache',
                'level': 'info',
                'message': 'Ratio de cache faible, optimiser la stratégie de mise en cache'
            })

        # Recommandations stock
        stock_alerts = app_metrics.get('stock', {}).get('alerts', 0)
        if stock_alerts > 10:
            recommendations.append({
                'type': 'stock',
                'level': 'warning',
                'message': f'{stock_alerts} alertes de stock nécessitent une attention'
            })

        return recommendations