from rest_framework import status
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.permissions import IsAuthenticated
from django.shortcuts import get_object_or_404
from .models import ResponsableEntrepot
from .serializers import (
    ResponsableEntrepotSerializer,
    ResponsableEntrepotCreateSerializer,
    ResponsableEntrepotCreateWithUserSerializer,
    ResponsableEntrepotRapportSerializer
)
from base.views import IsAdmin, isResponsable_Magasin


class ResponsableEntrepotListCreateView(APIView):
    permission_classes = [IsAuthenticated, IsAdmin]

    def get(self, request):
        """Lister tous les responsables d'entrepôt"""
        responsables = ResponsableEntrepot.objects.all().select_related('user', 'entrepot')

        # Filtrer par entrepôt si spécifié
        entrepot_id = request.query_params.get('entrepot_id')
        if entrepot_id:
            responsables = responsables.filter(entrepot_id=entrepot_id)

        # Filtrer par statut actif
        actif = request.query_params.get('actif')
        if actif is not None:
            responsables = responsables.filter(actif=actif.lower() == 'true')

        serializer = ResponsableEntrepotSerializer(responsables, many=True)
        return Response(serializer.data)

    def post(self, request):
        """Créer un nouveau responsable entrepôt avec son utilisateur"""
        serializer = ResponsableEntrepotCreateWithUserSerializer(data=request.data)
        if serializer.is_valid():
            responsable = serializer.save()
            # Retourner les détails du responsable créé
            response_serializer = ResponsableEntrepotSerializer(responsable)
            return Response({
                'responsable': response_serializer.data,
                'message': 'Responsable entrepôt créé avec succès'
            }, status=status.HTTP_201_CREATED)
        return Response({
            'errors': serializer.errors,
            'message': 'Erreur lors de la création du responsable entrepôt'
        }, status=status.HTTP_400_BAD_REQUEST)


class ResponsableEntrepotDetailView(APIView):
    permission_classes = [IsAuthenticated]

    def get_object(self, id):
        return get_object_or_404(ResponsableEntrepot, id=id)

    def get(self, request, id):
        """Récupérer un responsable d'entrepôt spécifique"""
        responsable = self.get_object(id)
        serializer = ResponsableEntrepotSerializer(responsable)
        return Response(serializer.data)

    def put(self, request, id):
        """Mettre à jour un responsable d'entrepôt"""
        responsable = self.get_object(id)
        serializer = ResponsableEntrepotSerializer(responsable, data=request.data, partial=True)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def delete(self, request, id):
        """Supprimer (désactiver) un responsable d'entrepôt"""
        responsable = self.get_object(id)
        responsable.actif = False
        responsable.save()
        return Response(status=status.HTTP_204_NO_CONTENT)


class ResponsableEntrepotPerformanceView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request, id):
        """Récupérer les performances d'un responsable d'entrepôt"""
        responsable = get_object_or_404(ResponsableEntrepot, id=id)
        performance_data = responsable.get_performance()
        serializer = ResponsableEntrepotRapportSerializer(performance_data)
        return Response(serializer.data)


class ResponsableEntrepotRapportView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request, id):
        """Générer un rapport détaillé de l'entrepôt géré"""
        responsable = get_object_or_404(ResponsableEntrepot, id=id)
        rapport = responsable.generer_rapport_entrepot()
        return Response(rapport)


class ResponsableEntrepotByUserView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """Récupérer le responsable d'entrepôt pour l'utilisateur connecté"""
        try:
            responsable = ResponsableEntrepot.objects.get(
                user=request.user,
                actif=True
            )
            serializer = ResponsableEntrepotSerializer(responsable)
            return Response(serializer.data)
        except ResponsableEntrepot.DoesNotExist:
            return Response(
                {"error": "Aucun entrepôt assigné à cet utilisateur"},
                status=status.HTTP_404_NOT_FOUND
            )


class ResponsableEntrepotStatsView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """Statistiques générales des responsables d'entrepôt"""
        from django.db.models import Count, Avg

        stats = {
            'total_responsables': ResponsableEntrepot.objects.count(),
            'responsables_actifs': ResponsableEntrepot.objects.filter(actif=True).count(),
            'entrepots_avec_responsable': ResponsableEntrepot.objects.filter(actif=True).values('entrepot').distinct().count(),
            'performance_moyenne': {}
        }

        # Calculer la performance moyenne
        responsables_actifs = ResponsableEntrepot.objects.filter(actif=True)
        if responsables_actifs.exists():
            total_produits = sum(r.get_performance()['total_produits_geres'] for r in responsables_actifs)
            total_alertes = sum(r.get_performance()['stocks_en_alerte'] for r in responsables_actifs)

            stats['performance_moyenne'] = {
                'produits_par_responsable': round(total_produits / responsables_actifs.count(), 2),
                'taux_alerte_moyen': round((total_alertes / total_produits * 100) if total_produits > 0 else 0, 2)
            }

        return Response(stats)
