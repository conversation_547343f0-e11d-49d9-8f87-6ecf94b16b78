# serializer.py
from rest_framework import serializers
from .models import Note, User
from django.contrib.auth import authenticate
from rest_framework_simplejwt.tokens import RefreshToken
from .utils import generate_username


class NoteSerializer(serializers.ModelSerializer):
    class Meta:
        model = Note
        fields = ['id', 'description', 'owner']
        read_only_fields = ['owner']

class UserSerializer(serializers.ModelSerializer):
    username = serializers.CharField(required=False)
    email = serializers.EmailField(required=False, allow_blank=True, allow_null=True)  # Email optionnel
    adresse = serializers.CharField(required=False, allow_blank=True)  # Adresse optionnelle
    avatar = serializers.ImageField(write_only=True, required=False)
    avatar_url =serializers.SerializerMethodField()

    class Meta:
        model = User
        fields = [
            'id_utilisateur', 'username', 'nom', 'prenom', 'email',
            'telephone', 'adresse', 'role', 'password', 'statut', 'createdAt','avatar','avatar_url',
        ]
        read_only_fields = ['id_utilisateur', 'statut', 'createdAt']
        extra_kwargs = {
            'password': {'write_only': True},
            'telephone': {'required': True},
        }

    def get_avatar_url(self, obj):
        request = self.context.get('request')
        try:
            if obj.avatar and hasattr(obj.avatar, 'url') and request:
                return request.build_absolute_uri(obj.avatar.url)
        except Exception as e:
            print(f"[Erreur Avatar URL] : {e}")
        return None

    def validate_role(self, value):
        roles = [role[0] for role in User.Role.choices]
        if value not in roles:
            raise serializers.ValidationError(
                f"Rôle invalide. Les rôles valides sont : {', '.join(roles)}"
            )
        return value

    def validate_email(self, value):
        # Si email est fourni, vérifier qu'il est unique
        if value and User.objects.filter(email=value).exists():
            raise serializers.ValidationError("Un utilisateur avec cet email existe déjà.")
        return value

    def validate_telephone(self, value):
        # Vérifier que le téléphone est unique
        if User.objects.filter(telephone=value).exists():
            raise serializers.ValidationError("Un utilisateur avec ce numéro de téléphone existe déjà.")
        return value

    def create(self, validated_data):
        # Si username n'est pas fourni, générer un automatiquement
        if 'username' not in validated_data or not validated_data['username']:
            nom = validated_data.get('nom', '')
            prenom = validated_data.get('prenom', '')

            # Gérer un username unique
            username = generate_username(nom, prenom)
            attempt = 0

            # Vérification si username existe déjà
            while User.objects.filter(username=username).exists() and attempt < 10:
                username = generate_username(nom, prenom)
                attempt += 1

            # Si après 10 tentatives pas username unique, ajouter un timestamp
            if attempt >= 10:
                import time
                username = f"{username}{int(time.time())}"

            validated_data['username'] = username

        # Création de l'utilisateur avec tous les champs requis
        user = User.objects.create_user(
            username=validated_data['username'],
            email=validated_data.get('email', None),  # Email optionnel
            password=validated_data['password'],
            nom=validated_data.get('nom', ''),
            prenom=validated_data.get('prenom', ''),
            telephone=validated_data.get('telephone', ''),
            adresse=validated_data.get('adresse', ''),  # Adresse optionnelle
            role=validated_data.get('role', User.Role.VENDEUR),
            statut=True  # Actif par défaut
        )
        return user



class LoginSerializer(serializers.Serializer):
    # Renommé pour refléter qu'il peut être un identifiant, téléphone ou email
    identifiant = serializers.CharField(required=True)
    password = serializers.CharField(required=True, write_only=True, style={'input_type': 'password'})

    def validate(self, attrs):
        #laissons la vue s'occuper de l'authentification
        return attrs

class AdminRegistrationSerializer(serializers.ModelSerializer):
    """Serializer spécialisé pour l'inscription d'administrateurs"""
    username = serializers.CharField(required=False)
    email = serializers.EmailField(required=False, allow_blank=True, allow_null=True)
    adresse = serializers.CharField(required=False, allow_blank=True)

    class Meta:
        model = User
        fields = [
            'id_utilisateur', 'username', 'nom', 'prenom', 'email',
            'telephone', 'adresse', 'password', 'statut', 'createdAt'
        ]
        read_only_fields = ['id_utilisateur', 'statut', 'createdAt']
        extra_kwargs = {
            'password': {'write_only': True},
            'telephone': {'required': True},
        }

    def validate_email(self, value):
        """Vérifier que l'email est unique si fourni"""
        if value and User.objects.filter(email=value).exists():
            raise serializers.ValidationError("Un utilisateur avec cet email existe déjà.")
        return value

    def validate_telephone(self, value):
        """Vérifier que le téléphone est unique"""
        if User.objects.filter(telephone=value).exists():
            raise serializers.ValidationError("Un utilisateur avec ce numéro de téléphone existe déjà.")
        return value

    def create(self, validated_data):
        # Générer un username si non fourni
        if 'username' not in validated_data or not validated_data['username']:
            nom = validated_data.get('nom', '')
            prenom = validated_data.get('prenom', '')
            username = generate_username(nom, prenom)
            attempt = 0

            while User.objects.filter(username=username).exists() and attempt < 10:
                username = generate_username(nom, prenom)
                attempt += 1

            if attempt >= 10:
                import time
                username = f"{username}{int(time.time())}"

            validated_data['username'] = username

        # Forcer le rôle ADMIN
        validated_data['role'] = User.Role.ADMIN

        # Créer l'utilisateur admin
        user = User.objects.create_user(
            username=validated_data['username'],
            email=validated_data.get('email', None),
            password=validated_data['password'],
            nom=validated_data.get('nom', ''),
            prenom=validated_data.get('prenom', ''),
            telephone=validated_data.get('telephone', ''),
            adresse=validated_data.get('adresse', ''),
            role=User.Role.ADMIN,
            statut=True
        )
        return user


class TokenSerializer(serializers.Serializer):
    refresh = serializers.CharField()
    access = serializers.CharField()

