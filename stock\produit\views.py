from rest_framework import status
from rest_framework.response import Response
from rest_framework.views import APIView
from .models import Produit
from .serializers import ProduitSerializer
from django.core.exceptions import ValidationError
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi


class ProduitListCreateView(APIView):
    @swagger_auto_schema(
        operation_description="Récupérer la liste de tous les produits",
        responses={
            200: openapi.Response(
                description="Liste des produits",
                schema=openapi.Schema(
                    type=openapi.TYPE_ARRAY,
                    items=openapi.Schema(type=openapi.TYPE_OBJECT)
                )
            ),
            400: "Bad Request"
        }
    )
    def get(self, request):
        produits = Produit.getAllProducts()
        serializer = ProduitSerializer(produits, many=True)
        return Response(serializer.data)

    @swagger_auto_schema(
        operation_description="Créer un nouveau produit",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=['nom', 'reference', 'categorie_nom', 'nom_magasin'],
            properties={
                'nom': openapi.Schema(type=openapi.TYPE_STRING),
                'reference': openapi.Schema(type=openapi.TYPE_STRING),
                'categorie_nom': openapi.Schema(type=openapi.TYPE_STRING),
                'nom_magasin': openapi.Schema(type=openapi.TYPE_STRING),
                'type_stock': openapi.Schema(type=openapi.TYPE_STRING),
                'prix': openapi.Schema(type=openapi.TYPE_NUMBER),
                'date_peremption': openapi.Schema(type=openapi.TYPE_STRING, format=openapi.FORMAT_DATE),
                'code_barre': openapi.Schema(type=openapi.TYPE_STRING),
                'marque': openapi.Schema(type=openapi.TYPE_STRING),
                'description': openapi.Schema(type=openapi.TYPE_STRING),
                'unite_mesure': openapi.Schema(type=openapi.TYPE_STRING),
                'prix_achat': openapi.Schema(type=openapi.TYPE_NUMBER),
                'prix_vente': openapi.Schema(type=openapi.TYPE_NUMBER),
                'TVA': openapi.Schema(type=openapi.TYPE_NUMBER)
            }
        ),
        responses={
            201: openapi.Response(
                description="Produit créé avec succès",
                schema=openapi.Schema(type=openapi.TYPE_OBJECT)
            ),
            400: "Bad Request"
        }
    )
    def post(self, request):
        serializer = ProduitSerializer(data=request.data)
        if serializer.is_valid():
            produit = serializer.save()
            return Response(ProduitSerializer(produit).data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class ProduitDetailView(APIView):
    @swagger_auto_schema(
        operation_description="Récupérer les détails d'un produit spécifique",
        responses={
            200: openapi.Response(
                description="Détails du produit",
                schema=openapi.Schema(type=openapi.TYPE_OBJECT)
            ),
            404: "Not Found"
        }
    )
    def get(self, request, id):
        produit = Produit.getProductById(id)
        if produit:
            serializer = ProduitSerializer(produit)
            return Response(serializer.data)
        return Response(status=status.HTTP_404_NOT_FOUND)

    @swagger_auto_schema(
        operation_description="Mettre à jour un produit existant",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'nom': openapi.Schema(type=openapi.TYPE_STRING),
                'reference': openapi.Schema(type=openapi.TYPE_STRING),
                'categorie_nom': openapi.Schema(type=openapi.TYPE_STRING),
                'nom_magasin': openapi.Schema(type=openapi.TYPE_STRING),
                'type_stock': openapi.Schema(type=openapi.TYPE_STRING),
                'prix': openapi.Schema(type=openapi.TYPE_NUMBER),
                'date_peremption': openapi.Schema(type=openapi.TYPE_STRING, format=openapi.FORMAT_DATE),
                'code_barre': openapi.Schema(type=openapi.TYPE_STRING),
                'marque': openapi.Schema(type=openapi.TYPE_STRING),
                'description': openapi.Schema(type=openapi.TYPE_STRING),
                'unite_mesure': openapi.Schema(type=openapi.TYPE_STRING),
                'prix_achat': openapi.Schema(type=openapi.TYPE_NUMBER),
                'prix_vente': openapi.Schema(type=openapi.TYPE_NUMBER),
                'TVA': openapi.Schema(type=openapi.TYPE_NUMBER)
            }
        ),
        responses={
            200: openapi.Response(
                description="Produit mis à jour",
                schema=openapi.Schema(type=openapi.TYPE_OBJECT)
            ),
            400: "Bad Request",
            404: "Not Found"
        }
    )
    def put(self, request, id):
        produit = Produit.getProductById(id)
        if produit:
            serializer = ProduitSerializer(produit, data=request.data, partial=True)
            if serializer.is_valid():
                produit.mettreÀJourProduit(serializer.validated_data)
                return Response(ProduitSerializer(produit).data)
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        return Response(status=status.HTTP_404_NOT_FOUND)

    @swagger_auto_schema(
        operation_description="Supprimer un produit",
        responses={
            200: "Produit supprimé avec succès",
            400: "Bad Request",
            404: "Not Found"
        }
    )
    def delete(self, request, id):
        produit = Produit.getProductById(id)
        if produit:
            try:
                resultat = produit.supprimer_produit()
                return Response({
                    "message": "Produit supprimé avec succès",
                    "stock_restant": resultat.get('nouvelle_quantite', 0)
                }, status=status.HTTP_200_OK)
            except ValidationError as e:
                return Response(
                    {"error": str(e)},
                    status=status.HTTP_400_BAD_REQUEST
                )
            except Exception as e:
                return Response(
                    {"error": f"Erreur lors de la suppression du produit : {str(e)}"},
                    status=status.HTTP_400_BAD_REQUEST
                )
        return Response(status=status.HTTP_404_NOT_FOUND)

           
            
    