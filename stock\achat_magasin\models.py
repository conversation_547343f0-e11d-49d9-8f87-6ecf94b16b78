import uuid
from django.db import models
from fournisseurs.models import Fournis<PERSON>ur
from produit.models import Produit
from django.core.exceptions import ValidationError

class AchatMagasin(models.Model):
    STATUS_CHOICES = [
        ('EN_ATTENTE', 'En attente'),
        ('CONFIRME', 'Confirmé'),
        ('LIVR<PERSON>', 'Liv<PERSON>'),
        ('ANNULE', 'Annulé'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    fournisseur = models.ForeignKey(Fournisseur, on_delete=models.CASCADE, related_name='achats_magasin')
    date_commande = models.DateField()
    date_reception_prevue = models.DateField()
    statut = models.CharField(max_length=20, choices=STATUS_CHOICES, default='EN_ATTENTE')
    montant_total = models.DecimalField(max_digits=10, decimal_places=2, default=0.00)
    date_creation = models.DateTimeField(auto_now_add=True)
    date_modification = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Achat Magasin {self.id} - {self.fournisseur.user.nom}"

    def clean(self):
        if self.date_reception_prevue < self.date_commande:
            raise ValidationError("La date de réception prévue ne peut pas être antérieure à la date de commande")
        
        if self.montant_total < 0:
            raise ValidationError("Le montant total ne peut pas être négatif")

    def creer_details_achat(self, produit, data):
        quantite = data.get('quantite', 1)
        prix_unitaire = data.get('prix_unitaire', produit.prix_achat)
        
        if quantite <= 0:
            raise ValidationError("La quantité doit être positive")
        if prix_unitaire <= 0:
            raise ValidationError("Le prix unitaire doit être positif")

        montant_total = quantite * prix_unitaire

        detail = DetailAchatMagasin.objects.create(
            achat=self,
            produit=produit,
            quantite_achetee=quantite,
            prix_unitaire=prix_unitaire,
            montant_total=montant_total,
        )

        self.montant_total += montant_total
        self.save()
        return detail

    def mettre_a_jour_achat(self, data):
        for field, value in data.items():
            if hasattr(self, field):
                setattr(self, field, value)
        self.full_clean()
        self.save()

    def confirmer_achat(self):
        if self.statut != 'EN_ATTENTE':
            raise ValidationError("Seuls les achats en attente peuvent être confirmés")
        self.statut = 'CONFIRME'
        self.save()

    def marquer_comme_livre(self):
        if self.statut != 'CONFIRME':
            raise ValidationError("Seuls les achats confirmés peuvent être marqués comme livrés")
        self.statut = 'LIVRE'
        self.save()

    def annuler_achat(self):
        if self.statut == 'LIVRE':
            raise ValidationError("Un achat livré ne peut pas être annulé")
        self.statut = 'ANNULE'
        self.save()

    def calculer_montant_total(self):
        total = self.details_achats.aggregate(total=models.Sum('montant_total'))['total'] or 0
        self.montant_total = total
        self.save(update_fields=['montant_total'])

class DetailAchatMagasin(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    achat = models.ForeignKey(AchatMagasin, on_delete=models.CASCADE, related_name='details_achats')
    produit = models.ForeignKey(Produit, on_delete=models.CASCADE)
    quantite_achetee = models.PositiveIntegerField()
    prix_unitaire = models.DecimalField(max_digits=10, decimal_places=2)
    montant_total = models.DecimalField(max_digits=10, decimal_places=2)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.quantite_achetee} x {self.produit.nom} - {self.montant_total}"

    def save(self, *args, **kwargs):
        self.montant_total = self.quantite_achetee * self.prix_unitaire
        super().save(*args, **kwargs)
        try:
            total = self.achat.details_achats.aggregate(total=models.Sum('montant_total'))['total'] or 0
            self.achat.montant_total = total
            self.achat.save()
        except Exception as e:
            raise ValidationError(f"Erreur lors de la mise à jour de l'achat : {str(e)}")