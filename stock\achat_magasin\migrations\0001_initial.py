# Generated by Django 5.2 on 2025-06-10 13:37

import django.db.models.deletion
import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('fournisseurs', '0001_initial'),
        ('produit', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='AchatMagasin',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('date_commande', models.DateField()),
                ('date_reception_prevue', models.DateField()),
                ('statut', models.CharField(choices=[('EN_ATTENTE', 'En attente'), ('CONFIRME', 'Confirmé'), ('LIVRE', 'Livré'), ('ANNULE', 'Annulé')], default='EN_ATTENTE', max_length=20)),
                ('montant_total', models.DecimalField(decimal_places=2, default=0.0, max_digits=10)),
                ('date_creation', models.DateTimeField(auto_now_add=True)),
                ('date_modification', models.DateTimeField(auto_now=True)),
                ('fournisseur', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='achats_magasin', to='fournisseurs.fournisseur')),
            ],
        ),
        migrations.CreateModel(
            name='DetailAchatMagasin',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('quantite_achetee', models.PositiveIntegerField()),
                ('prix_unitaire', models.DecimalField(decimal_places=2, max_digits=10)),
                ('montant_total', models.DecimalField(decimal_places=2, max_digits=10)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('achat', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='details_achats', to='achat_magasin.achatmagasin')),
                ('produit', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='produit.produit')),
            ],
        ),
    ]
