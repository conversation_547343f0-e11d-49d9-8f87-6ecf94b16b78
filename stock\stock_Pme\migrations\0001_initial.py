# Generated by Django 5.2 on 2025-05-20 08:11

import django.db.models.deletion
import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('entrepot', '0001_initial'),
        ('magasins', '0001_initial'),
        ('produit', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='MouvementStock',
            fields=[
                ('id_mouvement', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('quantite', models.PositiveIntegerField()),
                ('type_mouvement', models.CharField(choices=[('ENTREE', 'Entrée en stock'), ('SORTIE', 'Sortie de stock'), ('TRANSFERT', 'Transfert'), ('AJUSTEMENT', 'Ajustement')], max_length=20)),
                ('date_mouvement', models.DateTimeField(auto_now_add=True)),
                ('utilisateur', models.CharField(max_length=100)),
                ('notes', models.TextField(blank=True)),
                ('location', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='entrepot.location')),
                ('produit', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='produit.produit')),
            ],
            options={
                'verbose_name': 'Mouvement de stock',
                'verbose_name_plural': 'Mouvements de stock',
            },
        ),
        migrations.CreateModel(
            name='Stock',
            fields=[
                ('id_stock', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('quantite_disponible', models.IntegerField(default=0)),
                ('seuil_alerte', models.IntegerField(default=10)),
                ('updatedAt', models.DateField(auto_now=True)),
                ('entrepot', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='stocks', to='entrepot.entrepot')),
                ('location', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='entrepot.location')),
                ('magasin', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='stocks', to='magasins.magasin')),
                ('produit', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='produit.produit')),
            ],
            options={
                'verbose_name': 'Stock',
                'verbose_name_plural': 'Stocks',
                'indexes': [models.Index(fields=['produit', 'magasin'], name='stock_Pme_s_produit_a9b7ef_idx'), models.Index(fields=['entrepot'], name='stock_Pme_s_entrepo_c8afb3_idx')],
                'unique_together': {('produit', 'magasin', 'entrepot')},
            },
        ),
    ]
