from rest_framework import serializers
from .models import ResponsableEntrepot
from base.serializer import UserSerializer
from entrepot.serializers import EntrepotSerializer
from base.models import User
from base.utils import generate_username
import random


class ResponsableEntrepotSerializer(serializers.ModelSerializer):
    user_details = UserSerializer(source='user', read_only=True)
    entrepot_details = EntrepotSerializer(source='entrepot', read_only=True)
    performance = serializers.SerializerMethodField()

    class Meta:
        model = ResponsableEntrepot
        fields = [
            'id', 'user', 'user_details', 'entrepot', 'entrepot_details',
            'code_responsable', 'date_embauche', 'notes', 'actif',
            'performance', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']

    def get_performance(self, obj):
        return obj.get_performance()

    def validate_user(self, value):
        """Valider que l'utilisateur a le bon rôle"""
        if value.role != User.Role.RESPONSABLE_ENTREPOT:
            raise serializers.ValidationError(
                "L'utilisateur doit avoir le rôle RESPONSABLE_ENTREPOT"
            )
        return value

    def validate(self, data):
        """Validation globale"""
        # Vérifier qu'un responsable n'est pas déjà assigné à cet entrepôt
        if 'entrepot' in data and 'user' in data:
            existing = ResponsableEntrepot.objects.filter(
                entrepot=data['entrepot'],
                actif=True
            ).exclude(id=self.instance.id if self.instance else None)

            if existing.exists():
                raise serializers.ValidationError(
                    "Cet entrepôt a déjà un responsable actif assigné"
                )

        return data


class ResponsableEntrepotCreateSerializer(serializers.ModelSerializer):
    """Serializer spécialisé pour la création (utilisateur existant)"""

    class Meta:
        model = ResponsableEntrepot
        fields = [
            'user', 'entrepot', 'date_embauche', 'notes'
        ]

    def validate_user(self, value):
        if value.role != User.Role.RESPONSABLE_ENTREPOT:
            raise serializers.ValidationError(
                "L'utilisateur doit avoir le rôle RESPONSABLE_ENTREPOT"
            )

        # Vérifier que l'utilisateur n'est pas déjà responsable d'un autre entrepôt
        if ResponsableEntrepot.objects.filter(user=value, actif=True).exists():
            raise serializers.ValidationError(
                "Cet utilisateur est déjà responsable d'un autre entrepôt"
            )

        return value


class ResponsableEntrepotCreateWithUserSerializer(serializers.Serializer):
    """Serializer pour créer un responsable entrepôt avec son utilisateur"""

    # Données utilisateur
    nom = serializers.CharField(max_length=150)
    prenom = serializers.CharField(max_length=150)
    email = serializers.EmailField(required=False, allow_blank=True, allow_null=True)
    telephone = serializers.CharField(max_length=20)
    adresse = serializers.CharField(required=False, allow_blank=True)
    password = serializers.CharField(write_only=True)

    # Données responsable entrepôt
    entrepot = serializers.IntegerField()
    date_embauche = serializers.DateField()
    notes = serializers.CharField(required=False, allow_blank=True)

    def validate_telephone(self, value):
        """Vérifier que le téléphone n'existe pas déjà"""
        if User.objects.filter(telephone=value).exists():
            raise serializers.ValidationError("Ce numéro de téléphone est déjà utilisé")
        return value

    def validate_email(self, value):
        """Vérifier que l'email n'existe pas déjà (si fourni)"""
        if value and User.objects.filter(email=value).exists():
            raise serializers.ValidationError("Cette adresse email est déjà utilisée")
        return value

    def validate_entrepot(self, value):
        """Vérifier que l'entrepôt n'a pas déjà un responsable actif"""
        from entrepot.models import Entrepot
        try:
            entrepot = Entrepot.objects.get(id=value)
        except Entrepot.DoesNotExist:
            raise serializers.ValidationError("Entrepôt non trouvé")

        # Vérifier qu'il n'y a pas déjà un responsable actif pour cet entrepôt
        if ResponsableEntrepot.objects.filter(entrepot=entrepot, actif=True).exists():
            raise serializers.ValidationError("Cet entrepôt a déjà un responsable actif")

        return value

    def create(self, validated_data):
        # Extraire les données du responsable entrepôt
        entrepot_id = validated_data.pop('entrepot')
        date_embauche = validated_data.pop('date_embauche')
        notes = validated_data.pop('notes', '')

        # Générer un username unique
        nom = validated_data.get('nom', '')
        prenom = validated_data.get('prenom', '')
        username = generate_username(nom, prenom)
        attempt = 0

        while User.objects.filter(username=username).exists() and attempt < 10:
            username = generate_username(nom, prenom)
            attempt += 1

        if attempt >= 10:
            import time
            username = f"{username}{int(time.time())}"

        # Créer l'utilisateur avec le rôle RESPONSABLE_ENTREPOT
        user_data = {
            'username': username,
            'nom': validated_data['nom'],
            'prenom': validated_data['prenom'],
            'email': validated_data.get('email', ''),
            'telephone': validated_data['telephone'],
            'adresse': validated_data.get('adresse', ''),
            'password': validated_data['password'],
            'role': User.Role.RESPONSABLE_ENTREPOT
        }

        user_serializer = UserSerializer(data=user_data)
        user_serializer.is_valid(raise_exception=True)
        user = user_serializer.save()

        # Générer un code responsable entrepôt unique
        code_responsable = self._generate_code_responsable()

        # Créer le responsable entrepôt
        from entrepot.models import Entrepot
        entrepot = Entrepot.objects.get(id=entrepot_id)

        responsable = ResponsableEntrepot.objects.create(
            user=user,
            entrepot=entrepot,
            code_responsable=code_responsable,
            date_embauche=date_embauche,
            notes=notes
        )

        return responsable

    def _generate_code_responsable(self):
        """Générer un code responsable entrepôt unique"""
        while True:
            code = f"RESP-ENT-{random.randint(100000, 999999)}"
            if not ResponsableEntrepot.objects.filter(code_responsable=code).exists():
                return code


class ResponsableEntrepotRapportSerializer(serializers.Serializer):
    """Serializer pour les rapports de performance"""
    total_produits_geres = serializers.IntegerField()
    stocks_en_alerte = serializers.IntegerField()
    taux_alerte = serializers.FloatField()
    achats_mois = serializers.IntegerField()
    valeur_achats_mois = serializers.FloatField()
    capacite_entrepot = serializers.IntegerField()
    entrepot_actif = serializers.BooleanField()
