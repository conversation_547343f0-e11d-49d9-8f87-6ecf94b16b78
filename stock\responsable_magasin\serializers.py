from rest_framework import serializers
from .models import Responsable_magasin
from base.models import User
from base.serializer import UserSerializer
from base.utils import generate_username
from magasins.models import Magasin
import random

class ResponsableMagasinSerializer(serializers.ModelSerializer):
    user_details = serializers.SerializerMethodField()

    class Meta:
        model = Responsable_magasin
        fields = ['id', 'user', 'user_details', 'code_responsable_magasin', 'date_embauche', 'notes']
        read_only_fields = ['id']

    def get_user_details(self, obj):
        user = obj.user
        return {
            'id_utilisateur': user.id_utilisateur,
            'username': user.username,
            'nom': user.nom,
            'prenom': user.prenom,
            'email': user.email,
            'telephone': user.telephone,
            'adresse': user.adresse,
            'role': user.role,
            'statut': user.statut
        }

    def validate_user(self, value):
        if value.role != User.Role.RESPONSABLE_MAGASIN:
            raise serializers.ValidationError("L'utilisateur doit avoir le rôle RESPONSABLE_MAGASIN")
        return value


class ResponsableMagasinCreateSerializer(serializers.Serializer):
    """Serializer pour créer un responsable magasin avec son utilisateur"""

    # Données utilisateur
    nom = serializers.CharField(max_length=150)
    prenom = serializers.CharField(max_length=150)
    email = serializers.EmailField(required=False, allow_blank=True, allow_null=True)
    telephone = serializers.CharField(max_length=20)
    adresse = serializers.CharField(required=False, allow_blank=True)
    password = serializers.CharField(write_only=True)

    # Données responsable magasin
    magasin = serializers.PrimaryKeyRelatedField(
        queryset=Magasin.objects.all(),
        error_messages={
            'does_not_exist': 'Le magasin spécifié n\'existe pas.',
            'invalid': 'Veuillez fournir un ID de magasin valide.'
        }
    )
    date_embauche = serializers.DateField()
    notes = serializers.CharField(required=False, allow_blank=True)

    def validate_magasin(self, value):
        """Vérifier que le magasin n'a pas déjà un responsable"""
        if value.responsable_magasin:
            raise serializers.ValidationError("Ce magasin a déjà un responsable")
        return value

    def validate_telephone(self, value):
        """Vérifier que le téléphone n'existe pas déjà"""
        if User.objects.filter(telephone=value).exists():
            raise serializers.ValidationError("Ce numéro de téléphone est déjà utilisé")
        return value

    def validate_email(self, value):
        """Vérifier que l'email n'existe pas déjà (si fourni)"""
        if value and User.objects.filter(email=value).exists():
            raise serializers.ValidationError("Cette adresse email est déjà utilisée")
        return value

    def create(self, validated_data):
        # Extraire les données du responsable magasin
        magasin = validated_data.pop('magasin')
        date_embauche = validated_data.pop('date_embauche')
        notes = validated_data.pop('notes', '')

        # Générer un username unique
        nom = validated_data.get('nom', '')
        prenom = validated_data.get('prenom', '')
        username = generate_username(nom, prenom)
        attempt = 0

        while User.objects.filter(username=username).exists() and attempt < 10:
            username = generate_username(nom, prenom)
            attempt += 1

        if attempt >= 10:
            import time
            username = f"{username}{int(time.time())}"

        # Créer l'utilisateur avec le rôle RESPONSABLE_MAGASIN
        user_data = {
            'username': username,
            'nom': validated_data['nom'],
            'prenom': validated_data['prenom'],
            'email': validated_data.get('email', ''),
            'telephone': validated_data['telephone'],
            'adresse': validated_data.get('adresse', ''),
            'password': validated_data['password'],
            'role': User.Role.RESPONSABLE_MAGASIN
        }

        user_serializer = UserSerializer(data=user_data)
        user_serializer.is_valid(raise_exception=True)
        user = user_serializer.save()

        # Générer un code responsable magasin unique
        code_responsable = self._generate_code_responsable()

        # Créer le responsable magasin
        responsable = Responsable_magasin.objects.create(
            user=user,
            magasin=magasin,
            code_responsable_magasin=code_responsable,
            date_embauche=date_embauche,
            notes=notes
        )

        # Assigner automatiquement ce responsable au magasin
        magasin.responsable_magasin = responsable
        magasin.save()

        return responsable

    def _generate_code_responsable(self):
        """Générer un code responsable magasin unique"""
        while True:
            code = f"RESP-{random.randint(100000, 999999)}"
            if not Responsable_magasin.objects.filter(code_responsable_magasin=code).exists():
                return code

