# Generated by Django 5.2 on 2025-05-20 08:11

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('entrepot', '0001_initial'),
        ('magasins', '0001_initial'),
        ('produit', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Inventaire',
            fields=[
                ('id_inventaire', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('date_inventaire', models.DateField(auto_now_add=True)),
                ('statut', models.BooleanField(default=False)),
                ('entrepot', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='inventaires', to='entrepot.entrepot')),
                ('magasin', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='inventaires', to='magasins.magasin')),
                ('responsable', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='inventaires', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Inventaire',
                'verbose_name_plural': 'Inventaires',
            },
        ),
        migrations.CreateModel(
            name='DetailInventaire',
            fields=[
                ('id_detail', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('quantite_theorique', models.IntegerField()),
                ('quantite_reelle', models.IntegerField()),
                ('statut', models.BooleanField(default=True)),
                ('produit', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='produit.produit')),
                ('inventaire', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='details', to='inventaire.inventaire')),
            ],
            options={
                'verbose_name': 'Détail Inventaire',
                'verbose_name_plural': 'Détails Inventaires',
            },
        ),
        migrations.AddIndex(
            model_name='inventaire',
            index=models.Index(fields=['date_inventaire'], name='inventaire__date_in_723743_idx'),
        ),
        migrations.AddIndex(
            model_name='inventaire',
            index=models.Index(fields=['magasin'], name='inventaire__magasin_cb39e8_idx'),
        ),
        migrations.AddIndex(
            model_name='inventaire',
            index=models.Index(fields=['entrepot'], name='inventaire__entrepo_360cef_idx'),
        ),
        migrations.AddIndex(
            model_name='detailinventaire',
            index=models.Index(fields=['produit'], name='inventaire__produit_6b9a4a_idx'),
        ),
    ]
