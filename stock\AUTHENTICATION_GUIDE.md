# Guide d'Authentification et Création d'Utilisateurs

## Vue d'ensemble

Le système d'authentification a été modifié pour que :
- **Registration** : Seulement pour créer des comptes ADMIN
- **Création d'autres rôles** : Via les endpoints spécifiques de chaque app, seulement par l'admin

## Endpoints

### 1. Registration (Admin seulement)
```
POST /register/
```
**Description** : Crée uniquement des comptes administrateur

**Body** :
```json
{
    "nom": "<PERSON><PERSON>",
    "prenom": "<PERSON>",
    "email": "<EMAIL>",
    "telephone": "1234567890",
    "adresse": "123 Rue Admin",
    "password": "motdepasse123"
}
```

**Notes importantes** :
- ❌ **Pas besoin du champ `role`** - il sera automatiquement défini sur "ADMIN"
- ✅ Le système force le rôle administrateur pour toute inscription
- ✅ Seuls les champs utilisateur de base sont requis

### 2. Création de Responsable Magasin
```
POST /api/responsables-magasin/
```
**Permissions** : Admin seulement

**Body** :
```json
{
    "nom": "Martin",
    "prenom": "Sophie",
    "email": "<EMAIL>",
    "telephone": "0987654321",
    "adresse": "456 Avenue Commerce",
    "password": "motdepasse123",
    "magasin": 1,
    "date_embauche": "2024-01-15",
    "notes": "Responsable expérimenté"
}
```

**Réponse** :
```json
{
    "responsable": {
        "id": 1,
        "user_details": {
            "nom": "Martin",
            "prenom": "Sophie",
            "role": "RESPONSABLE_MAGASIN",
            "telephone": "0987654321"
        },
        "code_responsable_magasin": "RESP-123456",
        "date_embauche": "2024-01-15"
    },
    "message": "Responsable magasin créé avec succès"
}
```

### 3. Création de Vendeur
```
POST /api/vendeurs/
```
**Permissions** : Admin seulement

**Body** :
```json
{
    "nom": "Durand",
    "prenom": "Pierre",
    "email": "<EMAIL>",
    "telephone": "1122334455",
    "adresse": "789 Rue Vente",
    "password": "motdepasse123",
    "magasin": 1,
    "date_embauche": "2024-01-20",
    "taux_commission": 0.15,
    "notes": "Vendeur junior"
}
```

**Réponse** :
```json
{
    "vendeur": {
        "id": 1,
        "user_details": {
            "nom": "Durand",
            "prenom": "Pierre",
            "role": "VENDEUR",
            "telephone": "1122334455"
        },
        "code_vendeur": "VEND-789012",
        "date_embauche": "2024-01-20",
        "taux_commission": 0.15
    },
    "message": "Vendeur créé avec succès"
}
```

### 4. Création de Responsable Entrepôt
```
POST /api/responsables-entrepot/
```
**Permissions** : Admin seulement

**Body** :
```json
{
    "nom": "Leblanc",
    "prenom": "Marie",
    "email": "<EMAIL>",
    "telephone": "2233445566",
    "adresse": "456 Avenue Entrepôt",
    "password": "motdepasse123",
    "entrepot": 1,
    "date_embauche": "2024-01-25",
    "notes": "Responsable expérimentée en logistique"
}
```

**Réponse** :
```json
{
    "responsable": {
        "id": 1,
        "user_details": {
            "nom": "Leblanc",
            "prenom": "Marie",
            "role": "RESPONSABLE_ENTREPOT",
            "telephone": "2233445566"
        },
        "code_responsable": "RESP-ENT-345678",
        "date_embauche": "2024-01-25",
        "entrepot_details": {
            "nom": "Entrepôt Principal",
            "capacite_stockage": 5000
        }
    },
    "message": "Responsable entrepôt créé avec succès"
}
```

## Fonctionnalités Automatiques

### Génération automatique
- **Username** : Généré automatiquement à partir du nom et prénom
- **Codes** :
  - Responsable magasin : `RESP-XXXXXX`
  - Vendeur : `VEND-XXXXXX`
  - Responsable entrepôt : `RESP-ENT-XXXXXX`

### Validations
- Téléphone unique
- Email unique (si fourni)
- Codes uniques
- Rôles appropriés

## Workflow de Création

### Ordre logique de création :

1. **Admin se connecte** via `/login/`
2. **Admin crée l'infrastructure** :
   - Entreprises via `/api/entreprises/`
   - Magasins via `/api/magasins/` (sans responsable)
   - Entrepôts via `/api/entrepots/`
3. **Admin crée les utilisateurs** et les assigne :
   - `/api/responsables-magasin/` pour les responsables magasin (auto-assignés au magasin)
   - `/api/vendeurs/` pour les vendeurs (assignés au magasin)
   - `/api/responsables-entrepot/` pour les responsables entrepôt (assignés à l'entrepôt)
4. **Utilisateurs se connectent** avec leurs identifiants

### Logique d'assignation automatique :
- **Magasin** : Créé sans responsable, le responsable est auto-assigné lors de sa création
- **Entrepôt** : Le responsable est assigné lors de sa création
- **Vendeurs** : Assignés au magasin lors de leur création

## Sécurité

- Seuls les admins peuvent créer des utilisateurs
- Chaque endpoint vérifie les permissions
- Les mots de passe sont hashés automatiquement
- Les tokens JWT sont utilisés pour l'authentification

## Exemples d'utilisation

### Créer un admin (registration)
```bash
curl -X POST http://localhost:8000/register/ \
  -H "Content-Type: application/json" \
  -d '{
    "nom": "Admin",
    "prenom": "Principal",
    "telephone": "0000000000",
    "password": "motdepasse123",
    "email": "<EMAIL>",
    "adresse": "123 Admin Street"
  }'
```

### Créer un magasin (sans responsable)
```bash
curl -X POST http://localhost:8000/api/magasins/ \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "nom": "Magasin Centre-Ville",
    "entreprise": "uuid-de-entreprise"
  }'
```

### Créer un responsable magasin
```bash
curl -X POST http://localhost:8000/api/responsables-magasin/ \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "nom": "Martin",
    "prenom": "Sophie",
    "telephone": "0987654321",
    "password": "motdepasse123",
    "magasin": 1,
    "date_embauche": "2024-01-15"
  }'
```

### Créer un vendeur
```bash
curl -X POST http://localhost:8000/api/vendeurs/ \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "nom": "Durand",
    "prenom": "Pierre",
    "telephone": "1122334455",
    "password": "motdepasse123",
    "magasin": 1,
    "date_embauche": "2024-01-20"
  }'
```

### Créer un responsable entrepôt
```bash
curl -X POST http://localhost:8000/api/responsables-entrepot/ \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "nom": "Leblanc",
    "prenom": "Marie",
    "telephone": "2233445566",
    "password": "motdepasse123",
    "entrepot": 1,
    "date_embauche": "2024-01-25"
  }'
```
