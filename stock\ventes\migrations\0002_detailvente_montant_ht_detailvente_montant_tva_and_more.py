# Generated by Django 5.2 on 2025-05-21 03:31

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('clients', '0002_facture_alter_client_options_and_more'),
        ('entreprise', '0001_initial'),
        ('magasins', '0001_initial'),
        ('vendeurs', '0001_initial'),
        ('ventes', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='detailvente',
            name='montant_ht',
            field=models.DecimalField(decimal_places=2, default=1, max_digits=10),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='detailvente',
            name='montant_tva',
            field=models.DecimalField(decimal_places=2, default=1, max_digits=10),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='detailvente',
            name='prix_achat',
            field=models.DecimalField(decimal_places=2, default=2, max_digits=10),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='detailvente',
            name='taux_tva',
            field=models.DecimalField(decimal_places=2, default=20.0, max_digits=5),
        ),
        migrations.AddField(
            model_name='vente',
            name='entreprise',
            field=models.ForeignKey(default=1, on_delete=django.db.models.deletion.CASCADE, related_name='ventes', to='entreprise.entreprise'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='vente',
            name='magasin',
            field=models.ForeignKey(default=2, on_delete=django.db.models.deletion.CASCADE, related_name='ventes', to='magasins.magasin'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='vente',
            name='montant_ht',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=10),
        ),
        migrations.AddField(
            model_name='vente',
            name='montant_tva',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=10),
        ),
        migrations.AddIndex(
            model_name='vente',
            index=models.Index(fields=['magasin'], name='ventes_vent_magasin_5c5cf7_idx'),
        ),
    ]
