# Generated by Django 5.2 on 2025-05-24 09:28

import django.core.validators
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('magasins', '0001_initial'),
        ('responsable_magasin', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='responsable_magasin',
            options={'verbose_name': 'Responsable de magasin', 'verbose_name_plural': 'Responsables de magasin'},
        ),
        migrations.RemoveField(
            model_name='responsable_magasin',
            name='taux_commission',
        ),
        migrations.AddField(
            model_name='responsable_magasin',
            name='magasin',
            field=models.ForeignKey(default=1, on_delete=django.db.models.deletion.CASCADE, related_name='responsables', to='magasins.magasin'),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name='responsable_magasin',
            name='code_responsable_magasin',
            field=models.CharField(max_length=20, unique=True, validators=[django.core.validators.RegexValidator('^RESP-[0-9]{6}$', 'Le code doit être au format RESP-XXXXXX')]),
        ),
        migrations.AddIndex(
            model_name='responsable_magasin',
            index=models.Index(fields=['code_responsable_magasin'], name='responsable_code_re_7607b4_idx'),
        ),
        migrations.AddIndex(
            model_name='responsable_magasin',
            index=models.Index(fields=['magasin'], name='responsable_magasin_e1d2b5_idx'),
        ),
    ]
