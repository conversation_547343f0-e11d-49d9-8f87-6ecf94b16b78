# Generated by Django 5.2 on 2025-06-17 09:01

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('magasins', '0002_remove_magasin_localisations_magasin_localisation'),
    ]

    operations = [
        migrations.RunSQL(
            # Supprimer la colonne si elle existe
            sql="ALTER TABLE magasins_magasin DROP COLUMN IF EXISTS entrepot_id;",
            # Pas de rollback nécessaire car la colonne n'existe peut-être pas
            reverse_sql=migrations.RunSQL.noop
        ),
    ]
