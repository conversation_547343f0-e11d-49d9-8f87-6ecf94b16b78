# Récapitulatif des URLs par Rôle

## 🔐 Authentification (Tous)

### Registration & Login
```
POST /register/                    # Créer admin uniquement (pas de champ role)
POST /login/                       # Connexion tous rôles
POST /logout/                      # Déconnexion
POST /refresh/                     # Rafraîchir token
```

## 👨‍💼 Admin - Gestion des Utilisateurs

### Responsables Magasin
```
GET  /api/responsables-magasin/           # Lister tous les responsables magasin
POST /api/responsables-magasin/           # Créer responsable magasin + utilisateur
GET  /api/responsables-magasin/{id}/      # Détails d'un responsable magasin
PUT  /api/responsables-magasin/{id}/      # Modifier responsable magasin
DELETE /api/responsables-magasin/{id}/    # Supprimer responsable magasin
```

### Vendeurs
```
GET  /api/vendeurs/                       # Lister tous les vendeurs
POST /api/vendeurs/                       # Créer vendeur + utilisateur
GET  /api/vendeurs/{id}/                  # Détails d'un vendeur
PUT  /api/vendeurs/{id}/                  # Modifier vendeur
DELETE /api/vendeurs/{id}/                # Supprimer vendeur
```

### Responsables Entrepôt
```
GET  /api/responsables-entrepot/          # Lister tous les responsables entrepôt
POST /api/responsables-entrepot/          # Créer responsable entrepôt + utilisateur
GET  /api/responsables-entrepot/{id}/     # Détails d'un responsable entrepôt
PUT  /api/responsables-entrepot/{id}/     # Modifier responsable entrepôt
DELETE /api/responsables-entrepot/{id}/   # Supprimer responsable entrepôt

# Fonctionnalités spécialisées
GET  /api/responsables-entrepot/{id}/performance/  # Performance du responsable
GET  /api/responsables-entrepot/{id}/rapport/      # Rapport de l'entrepôt
GET  /api/responsables-entrepot/mon-entrepot/      # Mon entrepôt (utilisateur connecté)
GET  /api/responsables-entrepot/stats/             # Statistiques générales
```

## 🏢 Infrastructure (Admin)

### Entreprises
```
GET  /api/entreprises/                    # Lister entreprises
POST /api/entreprises/                    # Créer entreprise
GET  /api/entreprises/{id}/               # Détails entreprise
PUT  /api/entreprises/{id}/               # Modifier entreprise
DELETE /api/entreprises/{id}/             # Supprimer entreprise
```

### Magasins
```
GET  /api/magasins/                       # Lister magasins
POST /api/magasins/                       # Créer magasin (sans responsable)
GET  /api/magasins/{id}/                  # Détails magasin
PUT  /api/magasins/{id}/                  # Modifier magasin
DELETE /api/magasins/{id}/                # Supprimer magasin
```

### Entrepôts
```
GET  /api/entrepots/                      # Lister entrepôts
POST /api/entrepots/                      # Créer entrepôt
GET  /api/entrepots/{id}/                 # Détails entrepôt
PUT  /api/entrepots/{id}/                 # Modifier entrepôt
DELETE /api/entrepots/{id}/               # Supprimer entrepôt
```

## 📊 Gestion (Selon rôles)

### Produits
```
GET  /api/produits/                       # Lister produits
POST /api/produits/                       # Créer produit
GET  /api/produits/{id}/                  # Détails produit
PUT  /api/produits/{id}/                  # Modifier produit
DELETE /api/produits/{id}/                # Supprimer produit
```

### Stock
```
GET  /api/stock/                          # Gestion stock
POST /api/stock/                          # Mouvements stock
```

### Achats
```
GET  /api/achats/achats/                         # Lister achats
POST /api/achats/achats/                         # Créer achat
```

### Ventes
```
GET  /api/ventes/                         # Lister ventes
POST /api/ventes/                         # Créer vente
```

### Clients
```
GET  /api/clients/                        # Lister clients
POST /api/clients/                        # Créer client
```

### Fournisseurs
```
GET  /api/fournisseurs/                   # Lister fournisseurs
POST /api/fournisseurs/                   # Créer fournisseur
```

## 🔑 Permissions par Rôle

### ADMIN
- ✅ Toutes les URLs ci-dessus
- ✅ Création de tous les types d'utilisateurs
- ✅ Gestion complète du système

### RESPONSABLE_MAGASIN
- ✅ Gestion de son magasin
- ✅ Gestion des vendeurs de son magasin
- ✅ Gestion des ventes et clients
- ❌ Création d'autres responsables

### VENDEUR
- ✅ Gestion des ventes
- ✅ Gestion des clients
- ❌ Gestion des stocks
- ❌ Création d'utilisateurs

### RESPONSABLE_ENTREPOT
- ✅ Gestion de son entrepôt
- ✅ Gestion des stocks
- ✅ Gestion des achats
- ❌ Création d'utilisateurs

## 🚀 Workflow de Création

### 1. Ordre logique :
```
1. POST /register/                        # Admin s'inscrit
2. POST /login/                           # Admin se connecte
3. POST /api/entreprises/                 # Créer entreprise
4. POST /api/magasins/                    # Créer magasin (sans responsable)
5. POST /api/entrepots/                   # Créer entrepôt
6. POST /api/responsables-magasin/        # Créer responsable (auto-assigné)
7. POST /api/vendeurs/                    # Créer vendeurs
8. POST /api/responsables-entrepot/       # Créer responsable entrepôt
```

### 2. Codes générés automatiquement :
- **Responsable magasin** : `RESP-XXXXXX`
- **Vendeur** : `VEND-XXXXXX`  
- **Responsable entrepôt** : `RESP-ENT-XXXXXX`

## 📝 Notes importantes

- **Pas de champ `role`** dans `/register/` - automatiquement ADMIN
- **Magasins créés sans responsable** - assignation lors de la création du responsable
- **Permissions strictes** - seuls les admins peuvent créer des utilisateurs
- **Auto-assignation** - responsables s'assignent automatiquement à leur structure
