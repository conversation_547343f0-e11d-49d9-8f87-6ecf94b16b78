# Generated by Django 5.2 on 2025-05-27 07:39

import django.core.validators
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('entrepot', '0002_remove_entrepot_type_stock_location_type_stock'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='ResponsableEntrepot',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('code_responsable', models.CharField(max_length=20, unique=True, validators=[django.core.validators.RegexValidator('^RESP-ENT-[0-9]{6}$', 'Le code doit être au format RESP-ENT-XXXXXX')])),
                ('date_embauche', models.DateField()),
                ('notes', models.TextField(blank=True)),
                ('actif', models.Bo<PERSON>anField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('entrepot', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='responsables', to='entrepot.entrepot')),
                ('user', models.OneToOneField(limit_choices_to={'role': 'RESPONSABLE_ENTREPOT'}, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Responsable Entrepôt',
                'verbose_name_plural': 'Responsables Entrepôt',
                'unique_together': {('user', 'entrepot')},
            },
        ),
    ]
