from django.db.models.signals import post_save, pre_save, post_delete
from django.dispatch import receiver
from django.db import transaction
from .models import Produit
from stock_Pme.models import Stock
from entrepot.models import Entrepot
import logging

logger = logging.getLogger(__name__)

@receiver(post_save, sender=Produit)
def create_stock_for_product(sender, instance, created, **kwargs):
    """
    Crée automatiquement un stock pour un nouveau produit
    """
    if created:
        try:
            with transaction.atomic():
                entrepot = Entrepot.objects.get(type_stock=instance.type_stock)
                Stock.objects.create(
                    produit=instance,
                    entrepot=entrepot,
                    quantite=0,
                    magasin=instance.magasin
                )
                logger.info(f"Stock créé avec succès pour le produit {instance.nom}")
        except Entrepot.DoesNotExist:
            logger.error(f"Aucun entrepôt trouvé pour le type de stock {instance.type_stock}")
        except Exception as e:
            logger.error(f"Erreur lors de la création du stock: {str(e)}")

@receiver(pre_save, sender=Produit)
def validate_product(sender, instance, **kwargs):
    """
    Valide le produit avant la sauvegarde
    """
    if instance.prix <= 0:
        raise ValueError("Le prix doit être supérieur à 0")
    if instance.prix_achat <= 0:
        raise ValueError("Le prix d'achat doit être supérieur à 0")
    if instance.prix_vente <= 0:
        raise ValueError("Le prix de vente doit être supérieur à 0")
    if instance.TVA < 0:
        raise ValueError("La TVA ne peut pas être négative")
    if instance.code_barre and instance.code_barre < 0:
        raise ValueError("Le code-barre doit être positif")
    if instance.magasin and instance.magasin.entreprise != instance.entreprise:
        raise ValueError("Le magasin doit appartenir à la même entreprise que le produit")

@receiver(post_delete, sender=Produit)
def handle_product_deletion(sender, instance, **kwargs):
    """
    Gère la suppression d'un produit
    """
    try:
        with transaction.atomic():
            # Supprimer tous les stocks associés au produit
            Stock.objects.filter(produit=instance).delete()
            logger.info(f"Tous les stocks associés au produit {instance.nom} ont été supprimés")
    except Exception as e:
        logger.error(f"Erreur lors de la suppression des stocks: {str(e)}")

@receiver(post_save, sender=Produit)
def update_product_reference(sender, instance, **kwargs):
    """
    Met à jour la référence du produit si nécessaire
    """
    if not instance.reference:
        try:
            # Générer une référence basée sur la catégorie et un numéro séquentiel
            prefix = instance.categorie.nom[:3].upper() if instance.categorie else "PRD"
            last_product = Produit.objects.filter(
                reference__startswith=prefix
            ).order_by('-reference').first()
            
            if last_product and last_product.reference:
                last_number = int(last_product.reference[len(prefix):])
                new_number = last_number + 1
            else:
                new_number = 1
                
            instance.reference = f"{prefix}{new_number:04d}"
            instance.save(update_fields=['reference'])
            logger.info(f"Référence mise à jour pour le produit {instance.nom}: {instance.reference}")
        except Exception as e:
            logger.error(f"Erreur lors de la mise à jour de la référence: {str(e)}") 