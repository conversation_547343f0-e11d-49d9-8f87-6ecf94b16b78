# Generated by Django 5.2 on 2025-05-20 08:11

import django.db.models.deletion
import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('categorie', '0001_initial'),
        ('entreprise', '0001_initial'),
        ('magasins', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Produit',
            fields=[
                ('id_produit', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('nom', models.CharField(max_length=100)),
                ('reference', models.CharField(max_length=50, unique=True)),
                ('type_stock', models.CharField(choices=[('FINIS', 'Produit fini'), ('MATIERE', 'Matière première'), ('EMBALLAGE', 'Emballage'), ('SEMI_FINI', 'Produit semi-fini'), ('CONSOMMABLE', 'Consommable')], default='FINIS', max_length=50)),
                ('prix', models.DecimalField(decimal_places=2, max_digits=10)),
                ('date_peremption', models.DateField(blank=True, null=True)),
                ('code_barre', models.BigIntegerField(blank=True, null=True, unique=True)),
                ('marque', models.CharField(blank=True, max_length=50)),
                ('description', models.TextField(blank=True)),
                ('unite_mesure', models.CharField(choices=[('kg', 'Kilogramme'), ('g', 'Gramme'), ('L', 'Litre'), ('ml', 'Millilitre'), ('unité', 'Unité')], max_length=10)),
                ('prix_achat', models.DecimalField(decimal_places=2, max_digits=10)),
                ('prix_vente', models.DecimalField(decimal_places=2, max_digits=10)),
                ('TVA', models.FloatField(default=0.2)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('categorie', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='produits', to='categorie.categorie')),
                ('entreprise', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='entreprise.entreprise')),
                ('magasin', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='produits', to='magasins.magasin')),
            ],
            options={
                'verbose_name': 'Produit',
                'verbose_name_plural': 'Produits',
                'indexes': [models.Index(fields=['reference'], name='produit_pro_referen_d81bf4_idx'), models.Index(fields=['code_barre'], name='produit_pro_code_ba_f0d075_idx'), models.Index(fields=['categorie'], name='produit_pro_categor_44fe03_idx'), models.Index(fields=['magasin'], name='produit_pro_magasin_76d41d_idx')],
            },
        ),
    ]
