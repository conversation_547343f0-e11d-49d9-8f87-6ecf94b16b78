# Generated by Django 5.2 on 2025-05-20 08:11

import django.core.validators
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('magasins', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Vendeur',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('code_vendeur', models.CharField(max_length=20, unique=True, validators=[django.core.validators.RegexValidator('^VEND-[0-9]{6}$', 'Le code doit être au format VEND-XXXXXX')])),
                ('date_embauche', models.DateField()),
                ('taux_commission', models.FloatField(default=0.1)),
                ('notes', models.TextField(blank=True)),
                ('actif', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('magasin', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='vendeurs', to='magasins.magasin')),
                ('user', models.OneToOneField(limit_choices_to={'role': 'VENDEUR'}, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Vendeur',
                'verbose_name_plural': 'Vendeurs',
                'unique_together': {('user', 'magasin')},
            },
        ),
    ]
