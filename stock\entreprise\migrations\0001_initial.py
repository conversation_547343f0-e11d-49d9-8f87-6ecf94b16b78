# Generated by Django 5.2 on 2025-05-20 08:11

import django.core.validators
import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Entreprise',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('nom', models.CharField(max_length=100)),
                ('adresse', models.TextField()),
                ('nif', models.Char<PERSON>ield(max_length=20, unique=True, validators=[django.core.validators.RegexValidator('^[0-9]{13}$', 'Le NIF doit contenir 13 chiffres')])),
                ('date_creation', models.DateField()),
                ('statut', models.BooleanField(default=True)),
            ],
        ),
    ]
