from rest_framework import serializers
from .models import Entreprise

class EntrepriseSerializer(serializers.ModelSerializer):
    class Meta : 
        model = Entreprise
        fields = '__all__'
    
    def validate_nif(self, value):
        if not value.isdigit() or len(value) != 13:
            raise serializers.ValidationError("Le NIF doit contenir exactement 13 chiffres")
        return value

class RapportEntrepriseSerializer(serializers.Serializer):
    total = serializers.IntegerField()
    actives = serializers.IntegerField()
    par_annee = serializers.ListField(child=serializers.DictField())
            