# Generated by Django 5.2 on 2025-05-20 08:11

import django.core.validators
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Responsable_magasin',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('code_responsable_magasin', models.CharField(max_length=20, unique=True, validators=[django.core.validators.RegexValidator('^VEND-[0-9]{6}$', 'Le code doit être au format VEND-XXXXXX')])),
                ('date_embauche', models.DateField()),
                ('taux_commission', models.FloatField(default=0.1)),
                ('notes', models.TextField(blank=True)),
                ('user', models.OneToOneField(limit_choices_to={'role': 'RESPONSABLE_MAGASIN'}, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
        ),
    ]
