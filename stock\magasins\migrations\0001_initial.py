# Generated by Django 5.2 on 2025-05-20 08:11

import django.core.validators
import django.db.models.deletion
import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('entreprise', '0001_initial'),
        ('responsable_magasin', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='LocalisationMagasin',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('adresse', models.TextField()),
                ('ville', models.CharField(max_length=100)),
                ('code_postal', models.CharField(max_length=20)),
                ('pays', models.CharField(max_length=100)),
                ('telephone', models.CharField(max_length=15, validators=[django.core.validators.RegexValidator('^\\+?[1-9]\\d{1,14}$', 'Format de téléphone invalide')])),
                ('email', models.EmailField(blank=True, max_length=254, null=True)),
                ('actif', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='Magasin',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('nom', models.CharField(max_length=100)),
                ('actif', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('entreprise', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='magasins', to='entreprise.entreprise')),
                ('localisations', models.ManyToManyField(related_name='magasins', to='magasins.localisationmagasin')),
                ('responsable_magasin', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='magasins_geres', to='responsable_magasin.responsable_magasin')),
            ],
            options={
                'verbose_name': 'Magasin',
                'verbose_name_plural': 'Magasins',
                'indexes': [models.Index(fields=['nom'], name='magasins_ma_nom_df0e6e_idx'), models.Index(fields=['responsable_magasin'], name='magasins_ma_respons_d93c7d_idx')],
            },
        ),
    ]
