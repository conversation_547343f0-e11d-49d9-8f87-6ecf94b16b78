from django.db.models.signals import pre_save, post_save, post_delete
from django.dispatch import receiver
from django.db import transaction
from .models import Magasin, LocalisationMagasin
from stock_Pme.models import Stock
from statistiques.models import Statistique
import logging

logger = logging.getLogger(__name__)

@receiver(pre_save, sender=Magasin)
def validate_magasin(sender, instance, **kwargs):
    """
    Valide le magasin avant la sauvegarde
    """
    # Ne pas vérifier les localisations lors de la création initiale
    if not instance._state.adding:
        if not hasattr(instance, 'localisation') or not instance.localisation:
            raise ValueError("Un magasin doit avoir une localisation")
        if not instance.responsable_magasin:
            raise ValueError("Un magasin doit avoir un responsable")
    
    # Vérifier si le nom est unique pour l'entreprise
    if Magasin.objects.filter(
        nom=instance.nom,
        entreprise=instance.entreprise
    ).exclude(id=instance.id).exists():
        raise ValueError("Un magasin avec ce nom existe déjà dans cette entreprise")

@receiver(post_save, sender=Magasin)
def create_initial_statistics(sender, instance, created, **kwargs):
    """
    Crée les statistiques initiales pour un nouveau magasin
    """
    if created:
        try:
            with transaction.atomic():
                Statistique.objects.create(
                    entreprise=instance.entreprise,
                    magasin=instance,
                    date_statistique=instance.created_at.date()
                )
                logger.info(f"Statistiques initiales créées pour le magasin {instance.nom}")
        except Exception as e:
            logger.error(f"Erreur lors de la création des statistiques: {str(e)}")

@receiver(post_delete, sender=Magasin)
def handle_magasin_deletion(sender, instance, **kwargs):
    """
    Gère la suppression d'un magasin et de ses éléments associés
    """
    try:
        with transaction.atomic():
            # 1. Gérer les vendeurs associés
            from vendeurs.models import Vendeur
            vendeurs = Vendeur.objects.filter(magasin=instance)
            for vendeur in vendeurs:
                vendeur.actif = False
                vendeur.save(update_fields=['actif'])
                logger.info(f"Vendeur {vendeur.user.get_full_name()} désactivé suite à la suppression du magasin {instance.nom}")

            # 2. Gérer le responsable magasin
            if instance.responsable_magasin:
                responsable = instance.responsable_magasin
                responsable.actif = False
                responsable.save(update_fields=['actif'])
                logger.info(f"Responsable {responsable.user.get_full_name()} désactivé suite à la suppression du magasin {instance.nom}")

            # 3. Gérer les stocks associés
            from stock_Pme.models import Stock
            stocks = Stock.objects.filter(magasin=instance)
            for stock in stocks:
                stock.delete()
                logger.info(f"Stock {stock.id} supprimé suite à la suppression du magasin {instance.nom}")

            # 4. Supprimer les statistiques associées
            from statistiques.models import Statistique
            Statistique.objects.filter(magasin=instance).delete()
            logger.info(f"Statistiques supprimées pour le magasin {instance.nom}")

            # 5. Supprimer la localisation (sera fait automatiquement par CASCADE)
            logger.info(f"Localisation supprimée pour le magasin {instance.nom}")

            logger.info(f"Magasin {instance.nom} et ses éléments associés supprimés avec succès")
    except Exception as e:
        logger.error(f"Erreur lors de la suppression du magasin et de ses éléments associés: {str(e)}")

@receiver(pre_save, sender=LocalisationMagasin)
def validate_localisation(sender, instance, **kwargs):
    """
    Valide la localisation avant la sauvegarde
    """
    if not instance.ville:
        raise ValueError("La ville est obligatoire")
    if not instance.adresse:
        raise ValueError("L'adresse est obligatoire")
    if not instance.code_postal:
        raise ValueError("Le code postal est obligatoire")
    if not instance.pays:
        raise ValueError("Le pays est obligatoire")
    if not instance.telephone:
        raise ValueError("Le téléphone est obligatoire")

@receiver(post_delete, sender=LocalisationMagasin)
def handle_localisation_deletion(sender, instance, **kwargs):
    """
    Gère la suppression d'une localisation
    """
    try:
        # Vérifier si c'était la localisation d'un magasin
        if hasattr(instance, 'magasin'):
            magasin = instance.magasin
            logger.warning(f"Le magasin {magasin.nom} n'a plus de localisation")
            # Désactiver le magasin
            magasin.actif = False
            magasin.save(update_fields=['actif'])
            logger.info(f"Magasin {magasin.nom} désactivé car il n'a plus de localisation")
    except Exception as e:
        logger.error(f"Erreur lors de la suppression de la localisation: {str(e)}") 
