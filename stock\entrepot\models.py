import uuid
from django.db import models
from django.urls import reverse
from django.core.exceptions import ValidationError

class Entrepot(models.Model):
    TYPES_STOCK = [
        ('FINIS', 'Produits finis'),
        ('MATIERE', 'Matières premières'),
        ('EMBALLAGE', 'Emballage et packaging'),
        ('SEMI_FINI', 'Produits semi-finis'),
        ('MARCHANDISE', 'Marchandises'),
        ('CONSOMMABLE', 'Consommables'),
    ]

    id_entrepot = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    nom = models.CharField(max_length=100)
    adresse = models.TextField()
    capacite_stockage = models.IntegerField()
    statut = models.BooleanField(default=True)

    def __str__(self):
        return f"{self.nom} (Entrepôt complet)"

    def get_absolute_url(self):
        return reverse('entrepot-rapport', kwargs={'entrepot_id': self.id_entrepot})

    def get_types_stock_acceptes(self):
        """Retourne tous les types de stock disponibles"""
        return [t[0] for t in self.TYPES_STOCK]

    def verifier_compatibilite_stock(self, type_produit):
        """Vérifie si le type de produit est compatible avec les types de stock acceptés par l'entrepôt"""
        return type_produit in self.get_types_stock_acceptes()

    def generer_rapport_stock(self):
        from stock_Pme.models import Stock
        stocks = Stock.objects.filter(entrepot=self).select_related('produit')
        
        # Organiser les stocks par type
        stocks_par_type = {}
        for type_stock, type_display in self.TYPES_STOCK:
            stocks_par_type[type_stock] = []
        
        for stock in stocks:
            if stock.produit.type_stock in stocks_par_type:
                stocks_par_type[stock.produit.type_stock].append({
                    'produit': stock.produit.nom,
                    'quantite': stock.quantite,
                    'seuil_alerte': stock.seuil_alerte
                })

        return {
            'entrepot': self.nom,
            'types_stock': [type_display for _, type_display in self.TYPES_STOCK],
            'stocks_par_type': stocks_par_type,
            'total_produits': stocks.count()
        }

    def clean(self):
        if self.capacite_stockage < 0:
            raise ValidationError("La capacité de stockage doit être positive.")

    def save(self, *args, **kwargs):
        is_new = self._state.adding
        super().save(*args, **kwargs)
        
        # Si c'est un nouvel entrepôt, créer automatiquement les locations pour chaque type de stock
        if is_new:
            for type_stock, type_display in self.TYPES_STOCK:
                Location.objects.create(
                    entrepot=self,
                    nom=f"Zone {type_display}",
                    description=f"Zone dédiée aux {type_display.lower()}",
                    type_stock=type_stock
                )

class Location(models.Model):
    entrepot = models.ForeignKey(Entrepot, on_delete=models.CASCADE)
    nom = models.CharField(max_length=100)
    description = models.TextField(blank=True)
    type_stock = models.CharField(max_length=20, choices=Entrepot.TYPES_STOCK)

    def __str__(self):
        return f"{self.nom} ({self.entrepot.nom} - {self.get_type_stock_display()})"

    def clean(self):
        # Vérifier que le type de stock de la location est accepté par l'entrepôt
        if self.type_stock not in self.entrepot.get_types_stock_acceptes():
            raise ValidationError(
                f"Cette location ne peut pas stocker des {self.get_type_stock_display()}. "
                f"L'entrepôt n'accepte que : {', '.join([type_display for _, type_display in self.entrepot.TYPES_STOCK])}"
            )