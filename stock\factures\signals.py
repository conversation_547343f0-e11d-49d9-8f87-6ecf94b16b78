from django.db.models.signals import post_save, pre_save, post_delete
from django.dispatch import receiver
from django.db import transaction
from .models import Facture
from stock.ventes.models import Vente
from statistiques.models import Statistique
import logging

logger = logging.getLogger(__name__)

@receiver(post_save, sender=Facture)
def update_statistics_after_facture(sender, instance, created, **kwargs):
    """
    Met à jour les statistiques après la création ou la modification d'une facture
    """
    try:
        with transaction.atomic():
            # Mettre à jour les statistiques de l'entreprise
            entreprise = instance.vente.entreprise
            magasin = instance.vente.magasin
            
            # Trouver ou créer les statistiques pour aujourd'hui
            stats, created = Statistique.objects.get_or_create(
                entreprise=entreprise,
                magasin=magasin,
                date_statistique=instance.date_emission.date()
            )
            
            if instance.statut == 'PAYEE':
                stats.factures_payees += 1
                stats.montant_impaye -= instance.montant_total
            elif instance.statut == 'EN_ATTENTE':
                stats.factures_emises += 1
                stats.montant_impaye += instance.montant_total
            elif instance.statut == 'ANNULEE':
                stats.factures_emises -= 1
                stats.montant_impaye -= instance.montant_total
            
            stats.save()
            
    except Exception as e:
        logger.error(f"Erreur lors de la mise à jour des statistiques: {str(e)}")

@receiver(pre_save, sender=Facture)
def validate_facture(sender, instance, **kwargs):
    """
    Valide la facture avant la sauvegarde
    """
    if instance.montant_total != instance.montant_ht + instance.montant_tva:
        raise ValueError("Le montant total doit être égal à la somme du montant HT et de la TVA")
    
    if instance.date_echeance and instance.date_echeance < instance.date_emission:
        raise ValueError("La date d'échéance ne peut pas être antérieure à la date d'émission")

@receiver(post_delete, sender=Facture)
def handle_facture_deletion(sender, instance, **kwargs):
    """
    Gère la suppression d'une facture
    """
    try:
        with transaction.atomic():
            # Mettre à jour les statistiques
            entreprise = instance.vente.entreprise
            magasin = instance.vente.magasin
            
            stats = Statistique.objects.filter(
                entreprise=entreprise,
                magasin=magasin,
                date_statistique=instance.date_emission.date()
            ).first()
            
            if stats:
                if instance.statut == 'PAYEE':
                    stats.factures_payees -= 1
                    stats.montant_impaye += instance.montant_total
                elif instance.statut == 'EN_ATTENTE':
                    stats.factures_emises -= 1
                    stats.montant_impaye -= instance.montant_total
                
                stats.save()
                
    except Exception as e:
        logger.error(f"Erreur lors de la mise à jour des statistiques après suppression: {str(e)}")

@receiver(post_save, sender=Vente)
def create_facture_after_vente(sender, instance, created, **kwargs):
    """
    Crée automatiquement une facture après la création d'une vente
    """
    if created:
        try:
            Facture.generer_facture(instance)
        except Exception as e:
            logger.error(f"Erreur lors de la génération de la facture: {str(e)}") 